"""
测试和优化模块
测试整个流程，优化性能和用户体验，确保生成的游戏可正常运行
"""

import os
import json
import time
import unittest
import configparser
from typing import List, Dict, Tuple, Any
from main_pipeline_controller import MainPipelineController

try:
    import renpy
    game_directory = renpy.config.gamedir
except:
    game_directory = os.getcwd()

config = configparser.ConfigParser()
config.read(rf"{game_directory}\config.ini", encoding='utf-8')

class GameGenerationTester:
    def __init__(self):
        self.config = config
        self.test_dir = os.path.join(game_directory, "tests")
        self.test_results_dir = os.path.join(self.test_dir, "results")
        os.makedirs(self.test_results_dir, exist_ok=True)
        
        # 测试配置
        self.test_config = {
            "quick_test": True,  # 快速测试模式
            "generate_sample_materials": True,  # 生成示例素材
            "test_all_modules": True,  # 测试所有模块
            "performance_test": True,  # 性能测试
            "output_validation": True  # 输出验证
        }

    def run_comprehensive_test(self) -> Dict:
        """运行综合测试"""
        print("=" * 60)
        print("开始文化遗产游戏生成系统综合测试")
        print("=" * 60)
        
        test_results = {
            "start_time": time.time(),
            "tests": {},
            "overall_success": True,
            "performance_metrics": {},
            "recommendations": []
        }
        
        try:
            # 1. 环境检查
            test_results["tests"]["environment"] = self._test_environment()
            
            # 2. 模块单元测试
            test_results["tests"]["modules"] = self._test_individual_modules()
            
            # 3. 集成测试
            test_results["tests"]["integration"] = self._test_integration()
            
            # 4. 性能测试
            test_results["tests"]["performance"] = self._test_performance()
            
            # 5. 输出验证
            test_results["tests"]["output_validation"] = self._test_output_validation()
            
            # 6. 生成测试报告
            test_results["completion_time"] = time.time()
            test_results["total_duration"] = test_results["completion_time"] - test_results["start_time"]
            
            # 分析测试结果
            test_results = self._analyze_test_results(test_results)
            
            # 保存测试报告
            self._save_test_report(test_results)
            
            print("=" * 60)
            print("综合测试完成")
            print("=" * 60)
            
            return test_results
            
        except Exception as e:
            test_results["overall_success"] = False
            test_results["error"] = str(e)
            print(f"测试过程中发生错误: {e}")
            return test_results

    def _test_environment(self) -> Dict:
        """测试环境检查"""
        print("\n[1/5] 环境检查")
        print("-" * 30)
        
        env_test = {
            "success": True,
            "checks": {},
            "warnings": [],
            "errors": []
        }
        
        # 检查Python环境
        try:
            import sys
            env_test["checks"]["python_version"] = sys.version
            print(f"✓ Python版本: {sys.version}")
        except Exception as e:
            env_test["errors"].append(f"Python环境检查失败: {e}")
            env_test["success"] = False
        
        # 检查必要的库
        required_packages = [
            "spacy", "PIL", "requests", "numpy", "sklearn"
        ]
        
        for package in required_packages:
            try:
                __import__(package)
                env_test["checks"][f"package_{package}"] = "installed"
                print(f"✓ {package} 已安装")
            except ImportError:
                env_test["warnings"].append(f"可选包 {package} 未安装")
                print(f"⚠ {package} 未安装 (可选)")
        
        # 检查配置文件
        config_checks = [
            ("CHATGPT", "gpt_key"),
            ("AI绘画", "if_cloud"),
            ("SOVITS", "if_cloud")
        ]
        
        for section, key in config_checks:
            try:
                value = self.config.get(section, key, fallback="")
                if value:
                    env_test["checks"][f"config_{section}_{key}"] = "configured"
                    print(f"✓ 配置项 {section}.{key} 已设置")
                else:
                    env_test["warnings"].append(f"配置项 {section}.{key} 未设置")
                    print(f"⚠ 配置项 {section}.{key} 未设置")
            except Exception as e:
                env_test["errors"].append(f"配置检查失败: {e}")
        
        return env_test

    def _test_individual_modules(self) -> Dict:
        """测试各个模块"""
        print("\n[2/5] 模块单元测试")
        print("-" * 30)
        
        module_tests = {
            "success": True,
            "modules": {},
            "total_modules": 0,
            "passed_modules": 0
        }
        
        # 测试素材预处理模块
        try:
            from material_preprocessor import MaterialPreprocessor
            preprocessor = MaterialPreprocessor()
            
            # 创建测试素材
            test_text = "这是一个测试文本，用于验证素材预处理功能。"
            test_result = preprocessor.process_text_material(test_text, "test_material")
            
            module_tests["modules"]["material_preprocessor"] = {
                "success": True,
                "test_result": "通过基础功能测试"
            }
            module_tests["passed_modules"] += 1
            print("✓ 素材预处理模块测试通过")
            
        except Exception as e:
            module_tests["modules"]["material_preprocessor"] = {
                "success": False,
                "error": str(e)
            }
            module_tests["success"] = False
            print(f"✗ 素材预处理模块测试失败: {e}")
        
        module_tests["total_modules"] += 1
        
        # 测试RAG检索模块
        try:
            from rag_retrieval import RAGRetrieval
            rag = RAGRetrieval()
            
            # 测试基础检索功能
            stats = rag.get_statistics()
            
            module_tests["modules"]["rag_retrieval"] = {
                "success": True,
                "statistics": stats
            }
            module_tests["passed_modules"] += 1
            print("✓ RAG检索模块测试通过")
            
        except Exception as e:
            module_tests["modules"]["rag_retrieval"] = {
                "success": False,
                "error": str(e)
            }
            module_tests["success"] = False
            print(f"✗ RAG检索模块测试失败: {e}")
        
        module_tests["total_modules"] += 1
        
        # 测试其他关键模块
        modules_to_test = [
            ("game_framework_generator", "GameFrameworkGenerator"),
            ("node_narrative_generator", "NodeNarrativeGenerator"),
            ("enhanced_image_generator", "EnhancedImageGenerator"),
            ("enhanced_audio_generator", "EnhancedAudioGenerator"),
            ("renpy_integrator", "RenpyIntegrator"),
            ("ui_design_module", "UIDesignModule")
        ]
        
        for module_name, class_name in modules_to_test:
            try:
                module = __import__(module_name)
                cls = getattr(module, class_name)
                instance = cls()
                
                module_tests["modules"][module_name] = {
                    "success": True,
                    "test_result": "模块初始化成功"
                }
                module_tests["passed_modules"] += 1
                print(f"✓ {module_name} 模块测试通过")
                
            except Exception as e:
                module_tests["modules"][module_name] = {
                    "success": False,
                    "error": str(e)
                }
                module_tests["success"] = False
                print(f"✗ {module_name} 模块测试失败: {e}")
            
            module_tests["total_modules"] += 1
        
        return module_tests

    def _test_integration(self) -> Dict:
        """测试集成功能"""
        print("\n[3/5] 集成测试")
        print("-" * 30)
        
        integration_test = {
            "success": True,
            "pipeline_test": {},
            "data_flow_test": {},
            "error_handling_test": {}
        }
        
        try:
            # 创建测试素材
            test_materials = self._create_test_materials()
            
            # 测试主流程控制器
            controller = MainPipelineController()
            
            if self.test_config["quick_test"]:
                # 快速测试模式：只测试流程初始化
                integration_test["pipeline_test"] = {
                    "success": True,
                    "message": "主流程控制器初始化成功"
                }
                print("✓ 主流程控制器初始化测试通过")
            else:
                # 完整集成测试（耗时较长）
                print("执行完整集成测试（这可能需要几分钟）...")
                results = controller.generate_complete_game(
                    material_paths=test_materials,
                    theme="测试主题",
                    reference_materials=["测试参考"]
                )
                
                integration_test["pipeline_test"] = {
                    "success": True,
                    "results_summary": {
                        "steps_completed": results.get("completion_report", {}).get("generation_summary", {}).get("steps_completed", 0),
                        "total_time": results.get("completion_report", {}).get("generation_summary", {}).get("total_time", 0)
                    }
                }
                print("✓ 完整集成测试通过")
            
        except Exception as e:
            integration_test["success"] = False
            integration_test["pipeline_test"] = {
                "success": False,
                "error": str(e)
            }
            print(f"✗ 集成测试失败: {e}")
        
        return integration_test

    def _test_performance(self) -> Dict:
        """测试性能"""
        print("\n[4/5] 性能测试")
        print("-" * 30)
        
        performance_test = {
            "success": True,
            "metrics": {},
            "benchmarks": {}
        }
        
        try:
            # 内存使用测试
            import psutil
            process = psutil.Process()
            
            initial_memory = process.memory_info().rss / 1024 / 1024  # MB
            performance_test["metrics"]["initial_memory_mb"] = initial_memory
            
            # 模块加载时间测试
            start_time = time.time()
            controller = MainPipelineController()
            load_time = time.time() - start_time
            
            performance_test["metrics"]["module_load_time"] = load_time
            performance_test["benchmarks"]["module_load_acceptable"] = load_time < 10.0  # 10秒内
            
            final_memory = process.memory_info().rss / 1024 / 1024  # MB
            memory_usage = final_memory - initial_memory
            performance_test["metrics"]["memory_usage_mb"] = memory_usage
            performance_test["benchmarks"]["memory_usage_acceptable"] = memory_usage < 500  # 500MB内
            
            print(f"✓ 模块加载时间: {load_time:.2f}秒")
            print(f"✓ 内存使用: {memory_usage:.2f}MB")
            
        except ImportError:
            performance_test["metrics"]["note"] = "psutil未安装，跳过详细性能测试"
            print("⚠ psutil未安装，跳过详细性能测试")
        except Exception as e:
            performance_test["success"] = False
            performance_test["error"] = str(e)
            print(f"✗ 性能测试失败: {e}")
        
        return performance_test

    def _test_output_validation(self) -> Dict:
        """测试输出验证"""
        print("\n[5/5] 输出验证")
        print("-" * 30)
        
        validation_test = {
            "success": True,
            "file_structure": {},
            "content_validation": {},
            "renpy_compatibility": {}
        }
        
        try:
            # 检查输出目录结构
            expected_dirs = ["game", "images", "audio"]
            output_dir = os.path.join(game_directory, "generated_game")
            
            if os.path.exists(output_dir):
                for dir_name in expected_dirs:
                    dir_path = os.path.join(output_dir, "game", dir_name)
                    exists = os.path.exists(dir_path)
                    validation_test["file_structure"][dir_name] = exists
                    
                    if exists:
                        print(f"✓ 目录 {dir_name} 存在")
                    else:
                        print(f"⚠ 目录 {dir_name} 不存在")
            else:
                validation_test["file_structure"]["note"] = "输出目录不存在，需要先运行完整生成流程"
                print("⚠ 输出目录不存在，需要先运行完整生成流程")
            
            # 验证Ren'Py脚本语法（简单检查）
            script_path = os.path.join(game_directory, "game", "generated_script.rpy")
            if os.path.exists(script_path):
                with open(script_path, 'r', encoding='utf-8') as f:
                    script_content = f.read()
                
                # 基本语法检查
                syntax_checks = {
                    "has_character_definitions": "define " in script_content,
                    "has_labels": "label " in script_content,
                    "has_dialogue": '"' in script_content,
                    "no_obvious_syntax_errors": not any(error in script_content for error in ["SyntaxError", "IndentationError"])
                }
                
                validation_test["renpy_compatibility"] = syntax_checks
                
                if all(syntax_checks.values()):
                    print("✓ Ren'Py脚本基本语法检查通过")
                else:
                    print("⚠ Ren'Py脚本可能存在语法问题")
            else:
                validation_test["renpy_compatibility"]["note"] = "Ren'Py脚本文件不存在"
                print("⚠ Ren'Py脚本文件不存在")
            
        except Exception as e:
            validation_test["success"] = False
            validation_test["error"] = str(e)
            print(f"✗ 输出验证失败: {e}")
        
        return validation_test

    def _create_test_materials(self) -> List[str]:
        """创建测试素材"""
        test_materials = []
        
        # 创建测试文本文件
        test_text_content = """
        雷州石狗文化是广东雷州半岛独特的文化遗产。
        石狗作为雷州人民的精神寄托，承载着深厚的历史文化内涵。
        传说中，石狗具有驱邪避灾、保佑平安的神奇功效。
        每当村民遇到困难时，都会向石狗祈求庇护。
        """
        
        test_text_path = os.path.join(self.test_dir, "test_material.txt")
        with open(test_text_path, 'w', encoding='utf-8') as f:
            f.write(test_text_content)
        test_materials.append(test_text_path)
        
        return test_materials

    def _analyze_test_results(self, test_results: Dict) -> Dict:
        """分析测试结果"""
        # 计算总体成功率
        total_tests = 0
        passed_tests = 0
        
        for test_category, results in test_results["tests"].items():
            if isinstance(results, dict) and "success" in results:
                total_tests += 1
                if results["success"]:
                    passed_tests += 1
        
        test_results["overall_success_rate"] = passed_tests / total_tests if total_tests > 0 else 0
        
        # 生成建议
        recommendations = []
        
        if test_results["overall_success_rate"] < 1.0:
            recommendations.append("部分测试未通过，建议检查配置和依赖")
        
        if test_results["tests"].get("environment", {}).get("warnings"):
            recommendations.append("建议安装所有推荐的依赖包以获得最佳体验")
        
        if test_results["tests"].get("performance", {}).get("benchmarks", {}).get("memory_usage_acceptable") == False:
            recommendations.append("内存使用较高，建议优化或增加系统内存")
        
        test_results["recommendations"] = recommendations
        
        return test_results

    def _save_test_report(self, test_results: Dict):
        """保存测试报告"""
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        report_path = os.path.join(self.test_results_dir, f"test_report_{timestamp}.json")
        
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(test_results, f, ensure_ascii=False, indent=2)
        
        # 生成可读的测试报告
        readable_report = self._generate_readable_report(test_results)
        readable_path = os.path.join(self.test_results_dir, f"test_report_{timestamp}.txt")
        
        with open(readable_path, 'w', encoding='utf-8') as f:
            f.write(readable_report)
        
        print(f"\n测试报告已保存:")
        print(f"详细报告: {report_path}")
        print(f"可读报告: {readable_path}")

    def _generate_readable_report(self, test_results: Dict) -> str:
        """生成可读的测试报告"""
        report_lines = [
            "=" * 60,
            "文化遗产游戏生成系统 - 测试报告",
            "=" * 60,
            f"测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}",
            f"总耗时: {test_results.get('total_duration', 0):.2f}秒",
            f"总体成功率: {test_results.get('overall_success_rate', 0):.1%}",
            "",
            "测试结果概览:",
            "-" * 30
        ]
        
        for test_name, results in test_results["tests"].items():
            if isinstance(results, dict) and "success" in results:
                status = "✓ 通过" if results["success"] else "✗ 失败"
                report_lines.append(f"{test_name}: {status}")
        
        if test_results.get("recommendations"):
            report_lines.extend([
                "",
                "建议:",
                "-" * 30
            ])
            for i, rec in enumerate(test_results["recommendations"], 1):
                report_lines.append(f"{i}. {rec}")
        
        report_lines.extend([
            "",
            "=" * 60,
            "测试完成"
        ])
        
        return "\n".join(report_lines)

# 使用示例和主函数
if __name__ == "__main__":
    tester = GameGenerationTester()
    
    # 运行综合测试
    results = tester.run_comprehensive_test()
    
    # 显示测试结果摘要
    print(f"\n测试摘要:")
    print(f"总体成功率: {results.get('overall_success_rate', 0):.1%}")
    print(f"测试耗时: {results.get('total_duration', 0):.2f}秒")
    
    if results.get("recommendations"):
        print(f"\n建议:")
        for i, rec in enumerate(results["recommendations"], 1):
            print(f"{i}. {rec}")
    
    if results.get("overall_success_rate", 0) >= 0.8:
        print("\n✓ 系统测试基本通过，可以开始使用")
    else:
        print("\n⚠ 系统测试存在问题，建议先解决相关问题")

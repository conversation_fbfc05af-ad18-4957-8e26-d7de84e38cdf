"""
UI设计与跳转逻辑模块
设计游戏界面和分支跳转逻辑，支持menu选择和label跳转
"""

import os
import json
import configparser
from typing import List, Dict, Tuple, Any

try:
    import renpy
    game_directory = renpy.config.gamedir
except:
    game_directory = os.getcwd()

config = configparser.ConfigParser()
config.read(rf"{game_directory}\config.ini", encoding='utf-8')

class UIDesignModule:
    def __init__(self):
        self.config = config
        self.ui_dir = os.path.join(game_directory, "game")
        self.screens_file = os.path.join(self.ui_dir, "screens.rpy")
        
        # 确保目录存在
        os.makedirs(self.ui_dir, exist_ok=True)
        
        # UI主题配置
        self.ui_theme = {
            "primary_color": "#8B4513",      # 棕色 - 文化遗产主题
            "secondary_color": "#D2691E",    # 橙棕色
            "accent_color": "#CD853F",       # 沙棕色
            "text_color": "#2F1B14",         # 深棕色
            "background_color": "#F5E6D3",   # 米色
            "hover_color": "#A0522D",        # 深棕色
            "selected_color": "#8B4513"      # 主色
        }

    def generate_ui_system(self, framework: Dict, narratives: List[Dict]) -> Dict:
        """生成完整的UI系统"""
        print("生成UI设计与跳转逻辑...")
        
        ui_components = {
            "screens": self.generate_custom_screens(),
            "menus": self.generate_menu_system(narratives),
            "navigation": self.generate_navigation_logic(narratives),
            "cultural_features": self.generate_cultural_ui_features(),
            "accessibility": self.generate_accessibility_features()
        }
        
        # 生成screens.rpy文件
        screens_content = self.combine_ui_components(ui_components)
        self.save_screens_file(screens_content)
        
        # 生成跳转逻辑文件
        navigation_content = self.generate_navigation_file(ui_components["navigation"])
        self.save_navigation_file(navigation_content)
        
        print("UI系统生成完成")
        return ui_components

    def generate_custom_screens(self) -> str:
        """生成自定义界面"""
        screens = []
        
        # 1. 主菜单界面
        main_menu = '''
# 主菜单界面
screen main_menu():
    tag menu
    
    # 背景
    add "gui/main_menu.png"
    
    # 标题
    text "文化遗产探索之旅" style "main_menu_title"
    
    # 菜单按钮
    vbox:
        style_prefix "main_menu"
        xalign 0.5
        yalign 0.7
        spacing 20
        
        textbutton "开始游戏" action Start()
        textbutton "载入游戏" action ShowMenu("load")
        textbutton "设置" action ShowMenu("preferences")
        textbutton "文化档案" action ShowMenu("cultural_archive")
        textbutton "退出游戏" action Quit(confirm=not main_menu)

# 主菜单样式
style main_menu_title:
    size 60
    color gui.accent_color
    font "fonts/cultural_font.ttf"
    text_align 0.5
    xalign 0.5
    ypos 200

style main_menu_button:
    size 30
    idle_color gui.idle_color
    hover_color gui.hover_color
    selected_color gui.selected_color
'''
        screens.append(main_menu)
        
        # 2. 文化档案界面
        cultural_archive = '''
# 文化档案界面
screen cultural_archive():
    tag menu
    
    add "gui/overlay/main_menu.png"
    
    # 标题
    text "文化档案" style "menu_title"
    
    # 档案内容
    viewport:
        scrollbars "vertical"
        mousewheel True
        xpos 100
        ypos 150
        xsize 1720
        ysize 800
        
        vbox:
            spacing 30
            
            # 石狗文化介绍
            frame:
                style_prefix "cultural_frame"
                has vbox
                spacing 10
                
                text "雷州石狗文化" style "cultural_title"
                text "雷州石狗是广东雷州半岛特有的文化遗产..." style "cultural_text"
                
            # 历史背景
            frame:
                style_prefix "cultural_frame"
                has vbox
                spacing 10
                
                text "历史背景" style "cultural_title"
                text "石狗文化起源于古代雷州..." style "cultural_text"
    
    # 返回按钮
    textbutton "返回" action Return() style "return_button"

# 文化档案样式
style cultural_frame:
    background Frame("gui/frame.png", 10, 10)
    padding (20, 20)
    margin (10, 10)

style cultural_title:
    size 36
    color gui.accent_color
    bold True

style cultural_text:
    size 24
    color gui.text_color
    text_align 0.0
'''
        screens.append(cultural_archive)
        
        # 3. 游戏内HUD
        game_hud = '''
# 游戏内HUD
screen game_hud():
    
    # 文化知识点提示
    if cultural_hint:
        frame:
            style_prefix "hint"
            xalign 1.0
            yalign 0.0
            xoffset -20
            yoffset 20
            
            text cultural_hint style "hint_text"
    
    # 进度指示器
    frame:
        style_prefix "progress"
        xalign 0.0
        yalign 0.0
        xoffset 20
        yoffset 20
        
        hbox:
            spacing 10
            text "进度:" style "progress_label"
            bar value current_progress range total_progress style "progress_bar"
    
    # 快捷菜单
    hbox:
        style_prefix "quick"
        xalign 1.0
        yalign 1.0
        xoffset -20
        yoffset -20
        spacing 10
        
        textbutton "历史" action ShowMenu("history")
        textbutton "存档" action ShowMenu("save")
        textbutton "设置" action ShowMenu("preferences")
        textbutton "文化档案" action ShowMenu("cultural_archive")

# HUD样式
style hint_frame:
    background Frame("gui/hint_frame.png", 10, 10)
    padding (15, 10)

style hint_text:
    size 20
    color "#FFFFFF"

style progress_frame:
    background Frame("gui/progress_frame.png", 10, 10)
    padding (10, 5)

style progress_label:
    size 18
    color gui.text_color

style progress_bar:
    xsize 200
    ysize 20

style quick_button:
    size 18
    padding (10, 5)
'''
        screens.append(game_hud)
        
        return "\n".join(screens)

    def generate_menu_system(self, narratives: List[Dict]) -> str:
        """生成菜单系统"""
        menu_system = []
        
        # 1. 动态选择菜单生成器
        choice_menu = '''
# 动态选择菜单
screen choice_menu(items):
    style_prefix "choice"
    
    # 背景遮罩
    add "#000000AA"
    
    # 选择框
    frame:
        style_prefix "choice_frame"
        xalign 0.5
        yalign 0.5
        
        vbox:
            spacing 20
            
            text "请选择您的行动:" style "choice_title"
            
            for item in items:
                textbutton item["text"] action item["action"] style "choice_button"

# 选择菜单样式
style choice_frame:
    background Frame("gui/choice_frame.png", 20, 20)
    padding (40, 30)
    minimum (600, 400)

style choice_title:
    size 28
    color gui.accent_color
    text_align 0.5
    xalign 0.5

style choice_button:
    size 24
    padding (20, 10)
    margin (5, 5)
    idle_background Frame("gui/button/choice_idle.png", 10, 10)
    hover_background Frame("gui/button/choice_hover.png", 10, 10)
'''
        menu_system.append(choice_menu)
        
        # 2. 文化知识测试菜单
        knowledge_test = '''
# 文化知识测试
screen knowledge_test(question, options, correct_answer):
    modal True
    
    # 背景
    add "#000000CC"
    
    # 测试框
    frame:
        style_prefix "test"
        xalign 0.5
        yalign 0.5
        
        vbox:
            spacing 30
            
            text "文化知识小测试" style "test_title"
            text question style "test_question"
            
            vbox:
                spacing 15
                for i, option in enumerate(options):
                    textbutton option action [
                        SetVariable("test_answer", i),
                        If(i == correct_answer,
                           [Show("test_result", result="correct"), Hide("knowledge_test")],
                           [Show("test_result", result="incorrect"), Hide("knowledge_test")])
                    ] style "test_option"

# 测试样式
style test_frame:
    background Frame("gui/test_frame.png", 20, 20)
    padding (50, 40)
    minimum (800, 500)

style test_title:
    size 32
    color gui.accent_color
    text_align 0.5
    xalign 0.5

style test_question:
    size 26
    color gui.text_color
    text_align 0.5
    xalign 0.5

style test_option:
    size 22
    padding (15, 8)
    margin (10, 5)
'''
        menu_system.append(knowledge_test)
        
        return "\n".join(menu_system)

    def generate_navigation_logic(self, narratives: List[Dict]) -> Dict:
        """生成导航逻辑"""
        navigation = {
            "node_map": {},
            "branch_logic": {},
            "unlock_conditions": {}
        }
        
        # 构建节点地图
        for narrative in narratives:
            node_id = narrative.get("node_id", "")
            if node_id:
                navigation["node_map"][node_id] = {
                    "label": f"node_{node_id}",
                    "title": narrative.get("node_description", ""),
                    "unlocked": node_id == "A1",  # 只有第一个节点默认解锁
                    "visited": False
                }
        
        # 生成分支逻辑
        branch_points = ["A3", "A5", "A7", "A9"]  # 关键分支点
        for branch_node in branch_points:
            if branch_node in navigation["node_map"]:
                navigation["branch_logic"][branch_node] = {
                    "choices": [
                        {
                            "text": "深入了解文化背景",
                            "action": f"cultural_deep_{branch_node}",
                            "requirement": None
                        },
                        {
                            "text": "继续主线剧情",
                            "action": f"main_continue_{branch_node}",
                            "requirement": None
                        },
                        {
                            "text": "探索支线故事",
                            "action": f"side_explore_{branch_node}",
                            "requirement": "cultural_knowledge >= 3"
                        }
                    ]
                }
        
        return navigation

    def generate_cultural_ui_features(self) -> str:
        """生成文化特色UI功能"""
        cultural_features = '''
# 文化特色功能

# 文化知识收集界面
screen cultural_collection():
    tag menu
    
    add "gui/overlay/main_menu.png"
    
    text "文化知识收集" style "menu_title"
    
    # 知识点网格
    viewport:
        scrollbars "vertical"
        mousewheel True
        xpos 100
        ypos 150
        xsize 1720
        ysize 800
        
        grid 3 3:
            spacing 30
            
            for knowledge in cultural_knowledge_list:
                frame:
                    style_prefix "knowledge_item"
                    xsize 500
                    ysize 200
                    
                    if knowledge["unlocked"]:
                        button:
                            action Show("knowledge_detail", knowledge=knowledge)
                            has vbox
                            spacing 10
                            
                            add knowledge["icon"] xalign 0.5
                            text knowledge["title"] style "knowledge_title"
                            text knowledge["description"] style "knowledge_desc"
                    else:
                        vbox:
                            spacing 10
                            add "gui/locked_icon.png" xalign 0.5
                            text "???" style "knowledge_locked"
    
    textbutton "返回" action Return() style "return_button"

# 文化时间线界面
screen cultural_timeline():
    tag menu
    
    add "gui/overlay/main_menu.png"
    
    text "文化历史时间线" style "menu_title"
    
    # 时间线
    viewport:
        scrollbars "horizontal"
        mousewheel True
        xpos 50
        ypos 150
        xsize 1820
        ysize 800
        
        hbox:
            spacing 100
            
            for event in timeline_events:
                frame:
                    style_prefix "timeline_event"
                    ysize 600
                    
                    vbox:
                        spacing 20
                        
                        text event["year"] style "timeline_year"
                        add event["image"] xsize 200 ysize 150
                        text event["title"] style "timeline_title"
                        text event["description"] style "timeline_desc"
    
    textbutton "返回" action Return() style "return_button"

# 文化地图界面
screen cultural_map():
    tag menu
    
    add "gui/cultural_map_bg.png"
    
    text "文化遗产地图" style "menu_title"
    
    # 地图热点
    for location in cultural_locations:
        if location["discovered"]:
            imagebutton:
                idle location["marker_idle"]
                hover location["marker_hover"]
                xpos location["x"]
                ypos location["y"]
                action Show("location_detail", location=location)
    
    textbutton "返回" action Return() style "return_button"
'''
        
        return cultural_features

    def generate_accessibility_features(self) -> str:
        """生成无障碍功能"""
        accessibility = '''
# 无障碍功能

# 字体大小调节
screen font_size_control():
    frame:
        style_prefix "font_control"
        xalign 1.0
        yalign 0.0
        xoffset -20
        yoffset 100
        
        vbox:
            spacing 10
            
            text "字体大小" style "control_label"
            
            hbox:
                spacing 10
                textbutton "小" action SetField(persistent, "font_size", 18)
                textbutton "中" action SetField(persistent, "font_size", 22)
                textbutton "大" action SetField(persistent, "font_size", 26)
                textbutton "特大" action SetField(persistent, "font_size", 30)

# 色彩对比度调节
screen contrast_control():
    frame:
        style_prefix "contrast_control"
        xalign 1.0
        yalign 0.0
        xoffset -20
        yoffset 200
        
        vbox:
            spacing 10
            
            text "对比度" style "control_label"
            
            bar:
                value FieldValue(persistent, "contrast", 1.0, max_is_zero=False, step=0.1)
                xsize 150

# 语音朗读控制
screen voice_reading_control():
    frame:
        style_prefix "voice_control"
        xalign 1.0
        yalign 0.0
        xoffset -20
        yoffset 300
        
        vbox:
            spacing 10
            
            text "语音朗读" style "control_label"
            
            textbutton "开启" action SetField(persistent, "voice_reading", True)
            textbutton "关闭" action SetField(persistent, "voice_reading", False)
'''
        
        return accessibility

    def combine_ui_components(self, components: Dict) -> str:
        """组合UI组件"""
        header = '''# UI设计与跳转逻辑
# 文化遗产严肃游戏界面系统

# 初始化变量
default cultural_hint = ""
default current_progress = 0
default total_progress = 100
default cultural_knowledge_list = []
default timeline_events = []
default cultural_locations = []

'''
        
        content_parts = [header]
        content_parts.append(components["screens"])
        content_parts.append(components["menus"])
        content_parts.append(components["cultural_features"])
        content_parts.append(components["accessibility"])
        
        return "\n".join(content_parts)

    def generate_navigation_file(self, navigation_data: Dict) -> str:
        """生成导航逻辑文件"""
        nav_content = '''# 导航逻辑文件

# 节点解锁系统
init python:
    def unlock_node(node_id):
        """解锁节点"""
        if node_id in node_map:
            node_map[node_id]["unlocked"] = True
            renpy.save_persistent()
    
    def is_node_unlocked(node_id):
        """检查节点是否解锁"""
        return node_map.get(node_id, {}).get("unlocked", False)
    
    def visit_node(node_id):
        """标记节点为已访问"""
        if node_id in node_map:
            node_map[node_id]["visited"] = True
            renpy.save_persistent()

# 分支选择逻辑
label choice_handler(choices):
    call screen choice_menu(choices)
    return

# 文化知识检查
label cultural_knowledge_check(required_level):
    if cultural_knowledge_level >= required_level:
        return True
    else:
        "您需要更多的文化知识才能解锁此选项。"
        return False

'''
        
        # 添加节点地图数据
        nav_content += f"\n# 节点地图数据\ndefault node_map = {json.dumps(navigation_data['node_map'], ensure_ascii=False, indent=4)}\n"
        nav_content += f"\n# 分支逻辑数据\ndefault branch_logic = {json.dumps(navigation_data['branch_logic'], ensure_ascii=False, indent=4)}\n"
        
        return nav_content

    def save_screens_file(self, content: str):
        """保存screens文件"""
        with open(self.screens_file, 'w', encoding='utf-8') as f:
            f.write(content)

    def save_navigation_file(self, content: str):
        """保存导航文件"""
        nav_file = os.path.join(self.ui_dir, "navigation.rpy")
        with open(nav_file, 'w', encoding='utf-8') as f:
            f.write(content)

# 使用示例
if __name__ == "__main__":
    ui_module = UIDesignModule()
    
    # 示例数据
    framework = {"theme": "文化遗产探索"}
    narratives = [
        {"node_id": "A1", "node_description": "开始探索"},
        {"node_id": "A2", "node_description": "发现线索"},
        {"node_id": "A3", "node_description": "重要选择"}
    ]
    
    # 生成UI系统
    ui_components = ui_module.generate_ui_system(framework, narratives)
    
    print("UI系统生成完成:")
    print(f"界面组件: {len(ui_components)}")
    print(f"screens.rpy文件已生成: {ui_module.screens_file}")

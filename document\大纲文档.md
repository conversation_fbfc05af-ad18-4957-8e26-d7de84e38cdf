# 大纲文档

1. **输入素材收集**：获取文化遗产相关的文本（包括PDF文档）、图像等原始素材。
2. **素材预处理**：对文本进行分段和摘要，对图像进行语义分析。文本使用 spaCy 进行分段，再调用 Deepseek-R1 等大模型逐段生成抽象性摘要，将各段摘要汇聚成“汇总文档”（SAD）；图像使用 CLIP-Interrogator 提取自然语言描述，构建可检索的视觉语义知识库（VSI）。该阶段确保信息浓缩且易于后续检索。
3. **游戏框架生成**：基于预处理结果，调用大语言模型（如 Deepseek-R1 Phase I）构建主线故事骨架和角色设定。输入为汇总文档和参考资料，输出为结构化的主线剧情节点（如“节点A1: …”）和角色简介。通过精心设计的提示词，控制信息密度，确保每个框架节点内容充实连贯。
4. **节点叙事与脚本生成**：对于每个故事节点（从主线骨架获得），迭代进行叙事细化和剧本生成。调用 Deepseek-R1 Phase II，输入该节点的上下文、参考资料（SAD检索的相关段落）及前文情节，输出包含该节点对话、旁白、场景说明等内容。同时在输出中按照规则标记如 `[Setting:]`、`[Music:]` 等指令，为后续图像和音频生成做准备。
5. **图像生成**：利用节点叙事中的场景与角色描述生成视觉素材。Deepseek-R1 Phase III 接收前一步的描述和角色概况，结合视觉知识库（VSI）选取参考图像，生成用于 Stable Diffusion 的提示词。将生成的正向和负向提示词输入预训练的 Stable Diffusion（如 SD 1.5-Anything-V5），生成符合情境的背景与角色立绘。所有生成的图像将回写到 VSI 中以丰富后续参考。
6. **音频生成**：为游戏制作背景音乐和角色语音。对带有 `[Music:]` 指令的语句，调用 MusicGen 模型生成背景音乐；对角色对话文本，使用 GPT-SoVITS 合成语音。输出音频文件与节点文本对齐，实现沉浸式体验。
7. **Ren’Py 整合**：将上述文本、图像、音频整合入 Ren’Py 剧本。定义角色（`define Character`）、场景（`scene`）、图片（`show`）、对话（`say` 语句）等脚本结构，并在合适位置播放音乐与语音。
8. **界面设计与跳转逻辑**：设计游戏界面元素和剧情分支。利用 Ren’Py 提供的 `menu:` 机制和 `jump` 标签实现玩家选择与分支跳转；可编写自定义 `screen` 实现复杂 UI。如题材需要，可为每个节点生成选择题（`menu`），使玩家能在不同选项下跳转到相应节点。
9. **输出结果**：最终输出一个可运行的 Ren’Py 游戏项目，包含 `.rpy` 剧本文件、场景背景、角色立绘、音乐和语音文件，实现完整的严肃游戏体验。

# 模块结构说明

### 素材预处理模块

- **目标**：压缩和结构化输入素材，使关键信息可检索。
- **输入**：原始文本（小说、历史文献等，可能来自PDF）和图像素材。
- **输出**：文本的**汇总文档**（SAD，Summarized Abstract Document）和图像的**视觉语义库存**（VSI）。SAD 为多个段落摘要组成的合并文件，VSI 为每张图像生成的描述列表。
- **模型/方法**：使用 **spaCy** 进行文本分段；使用 **Deepseek-R1**（或类似大语言模型）逐段生成抽象性摘要；使用 **CLIP-Interrogator** 提取图像描述。构建类似检索增强生成（RAG）的双模态知识库，便于后续阶段引用原始素材。

### 框架生成模块

- **目标**：基于整理后的素材生成游戏的主线剧情框架和角色设定。
- **输入**：来自预处理的汇总文档(SAD)、可能的参考文献、文化背景信息等。
- **输出**：结构化的剧情骨架，包括主线节点（含节点编号和简要描述）和角色介绍。例如输出“节点A1: …”，“角色X: …”等。
- **模型/方法**：调用 **Deepseek-R1**（Phase I），设计提示词引导模型输出高层故事结构。在提示词中应控制信息密度，确保每节点内容既不空洞也不过载。生成时可以引用检索到的资料，增加剧情与文化内涵的一致性。

### 剧情节点生成模块

- **目标**：为每个主线节点生成具体的叙事内容、对话和场景说明。
- **输入**：当前节点的剧情概要、角色信息，以及预处理阶段检索得到的相关文本片段（参考资料）。
- **输出**：包含节点内的对话文本、旁白、场景设置描述等。例如带有 “[Setting:]” 的场景说明、带有角色名称的台词列表等。
- **模型/方法**：使用 **Deepseek-R1**（Phase II）并结合 RAG 结构，根据节点上下文与引用资料生成内容。在生成结果中，对不同类型内容进行标记（如 `Setting`, `Music:`），为后续模块提供提示。示例如所示：
  - 带有 `[Setting:]` 的描述，将用于生成背景图像；
  - 对话内容将用于声优配音；
  - 含有 `Music:` 关键词的条目，将直接交给 MusicGen 生成音乐。

### 图像生成模块

- **目标**：依据剧情内容生成适配的背景图和角色立绘。
- **输入**：节点中的场景描述和角色形象（来自剧情节点输出）以及视觉语义库存中的相关描述。
- **输出**：游戏场景背景图、角色头像等图像文件。
- **模型/方法**：首先使用 **Deepseek-R1**（Phase III）将节点中的描述（如 `Setting:` 前缀文本和角色简介）与 VSI 中检索到的参考图像描述合成，生成精细的 Stable Diffusion 提示词。然后调用 **Stable Diffusion 1.5-Anything-V5** 等预训练图像生成模型，分别生成所需场景图和立绘。生成时包括“正向提示”与“负向提示”来控制画面风格（如所示例）。所有新生成的图像可加入 VSI 以丰富后续节点生成的参考。

### 音频生成模块

- **目标**：为游戏添加背景音乐和角色配音，增强沉浸感。
- **输入**：已生成的对话文本、旁白文本及节点中标记的音乐提示。
- **输出**：背景音乐文件（BGM）和角色语音音频文件（对话配音）。
- **模型/方法**：对剧情中出现的音乐指令（以 `Music:` 开头），直接调用 **MusicGen** 按提示生成背景音乐。对角色对话文本，使用 **GPT-SoVITS** 之类的语音合成模型生成自然语音。合成时需结合角色设置（性别、年龄、情感）选择合适的声音模型或调整语音参数。生成完成后，音频与文本一一对应，方便后续游戏播放。

### Ren’Py整合模块

- **目标**：将以上生成的文本、图像、音频资源组合成可执行的 Ren’Py 游戏剧本。
- **输入**：节点剧本（对话、旁白）、场景背景图、角色头像、语音与音乐文件。
- **输出**：一个包含 `.rpy` 脚本文件和资源文件的 Ren’Py 项目目录。
- **模型/方法**：编写 Ren’Py 脚本逻辑：
  - 使用 `define` 语句注册角色（Character），例如 `define e = Character("Alice")`；
  - 每个节点对应一个 `label`，使用 `scene` 和 `show` 切换背景和立绘；
  - 使用 `say` 语句实现对话，格式如 `e "Hello, world!"`；
  - 在合适位置使用 `play music "bgm.mp3"` 和 `voice "line1.mp3"` 播放音乐和语音。
  - 遇到需要选择时，使用 `menu:` 创建选项，并通过 `jump` 标签跳转到相应的剧情节点。
  - 最终生成的脚本必须逻辑自洽、语法正确，所有引用的文件路径正确设置。

### UI设计与跳转逻辑模块

- **目标**：设计用户界面和剧情分支交互，使游戏可玩性与用户参与度提升。
- **输入**：剧情节点列表、节点之间的转移关系（分支信息）。
- **输出**：Ren’Py 脚本中的界面元素代码和分支逻辑。例如自定义的 `screen` 界面定义、`menu` 选项及 `jump` 跳转代码。
- **模型/方法**：按照 Ren’Py 视觉小说模式，设计简洁易懂的界面。使用 Ren’Py 的 `menu` 语法实现分支跳转（用户选择选项后跳转到对应 `label`）。必要时可编写自定义屏幕（`screen`）展示额外 UI（如任务列表、道具栏）。通过这些设计，将线性剧情扩展为多结局或者支线剧情，增加游戏深度。

# 提示词文档

以下示例针对各模块给出多轮提示词对话，演示如何调用不同模型和结构化输出。

### 模块：素材预处理

- **目标**：对输入的文本和图像进行分段、摘要和描述提取。

示例对话：

- **系统**：你是一个多模态数据预处理助手，需要将输入的文档和图片转换成可检索的摘要和描述。
- **用户**：输入资料如下：文本：“这是粤夷意义…”； 图像【图像1】为雷州石狗图片。请进行预处理。
- **代理**：好的，我执行以下步骤：
  1. **文本分段**：调用 spaCy 对文本进行句子分段。
  2. **摘要生成**：对于每个文本片段，我调用 Deepseek-R1 模型，提示词为：“摘要以下文本：这是粤夷意义…”，生成该片段的抽象性摘要。
  3. **汇总文档**：将所有片段摘要合并，得到汇总文档(SAD)。
  4. **图像分析**：对【图像1】使用 CLIP-Interrogator 提取描述，得到提示语句“石狗雕像, 雨天背景, 仿古雕刻风格”加入视觉语义库存（VSI）。
- **代理**：输出：SAD 文本（如“摘要：……”），以及图像1的描述“图像1: 石狗雕像在雨天中的特写”存入VSI。

### 模块：游戏框架生成

- **目标**：根据预处理结果生成主线剧情结构。

示例对话：

- **系统**：现在需要根据汇总文档生成游戏的主线剧情框架。
- **用户**：使用以上生成的汇总文档和石狗文化参考资料，给出一个结构化的主线剧情概述。
- **代理**：我将调用 Deepseek-R1（Phase I），提示词：“利用以下文化遗产摘要和参考资料，生成游戏主线故事节点。请列出节点编号和简要描述。”
- **代理**（调用Deepseek-R1输出）：
  - **节点A1**：历史系毕业生林小满在雷州博物馆参与石狗考古调查，发现明代八卦石狗基座血迹…（结合参考文献细节）。
  - **节点A2**：林小满在濛濛细雨中回到村里，村民讲述石狗的神奇传说…
  - …（共14个主线节点及3个支线节点）

### 模块：剧情节点生成

- **目标**：为某个剧情节点生成详细对话与场景描述，并标注相关指令。

示例对话：

- **系统**：使用节点A1的概要生成该节点的对话脚本和描述。

- **用户**：节点A1概要：“林小满在 Namei 村发现血迹，村民提及石狗传说”。参考资料片段：“石狗见主落泪后大雨倾盆”。

- **代理**：好的，我先调用 Deepseek-R1（Phase II），提示词：“根据节点A1概要和参考资料，生成以下内容：`[Setting:]` 开头的场景描写；带角色名称的对话；`[Music:]` 开头的音效提示。”

- **代理**（调用Deepseek-R1输出示例）：

  ```
  [Setting: 暮色下的乡村古庙，石狗雕像被雨水冲刷]
  祖母（激动）：“看！六十年前石狗为主人流泪，那场暴雨就是神迹！”
  林小满（记录）：“石狗在大雨中流血般的眼泪……”
  [Music: 雨声渐强，远处雷鸣]
  ```

  - 其中“[Setting:]”和角色对话将用于场景和语音合成；“[Music:]”被识别为音乐指令。

### 模块：图像生成

- **目标**：根据节点描述生成对应场景和角色图像。

示例对话：

- **系统**：现在根据上述场景描述生成背景图像。
- **用户**：节点A1的场景：“暮色下雨中的乡村古庙，有石狗雕像”。请生成Stable Diffusion提示词并输出图像。
- **代理**：先调用 Deepseek-R1（Phase III）生成图像提示：“majestic weathered stone dog statue in a rainy abandoned village temple at dusk… cinematic, photorealistic”。然后调用 **Stable Diffusion 1.5-Anything-V5**：
  - **正向提示**：`"majestic stone dog statue, rain-drenched temple courtyard at dusk, detailed granite texture, dramatic lighting"`
  - **负向提示**：`"lowres, cartoon, modern buildings, bright sunlight"`
- **代理**（调用Stable Diffusion输出）：生成一幅逼真的石狗场景图像（保存为 `scene_A1.png`）。

### 模块：音频生成

- **目标**：为对话和背景生成音频。

示例对话：

- **系统**：为节点A1添加背景音乐和角色配音。
- **用户**：节点A1含有对话和音乐指令，请生成音频。
- **代理**：检查对话，发现最后一行含有`[Music: 雨声渐强，远处雷鸣]`。
  - 调用 **MusicGen**，提示词为“rainstorm intensifies, thunder sound effect building”，生成背景音乐 `bgm_A1.mp3`。
  - 对话文本“祖母：‘看！六十年前石狗为主人流泪…’”，调用 **GPT-SoVITS** 生成祖母配音 `voice_grandma_A1.wav`，调用GPT-SoVITS生成林小满配音 `voice_lin_A1.wav`。
- **代理**：输出音乐文件和两段角色语音。

### 模块：Ren’Py整合

- **目标**：将所有文本和资源编写成 Ren’Py 剧本。

示例对话：

- **系统**：整合上述内容，生成 Ren’Py 剧本片段。

- **用户**：节点A1需要写成 Ren’Py 脚本，角色：祖母 (Grandma)、林小满 (Lin)，场景背景 `scene_A1.png`，语音与音乐文件已生成。

- **代理**：好的，我将生成如下 Ren’Py 脚本代码：

  ```
  define grandma = Character("祖母", color="#ffcc00")
  define lin = Character("林小满", color="#00ccff")
  
  label nodeA1:
      scene scene_A1 with fade
      play music "bgm_A1.mp3" fadein 1.0
      grandma "看！六十年前石狗为主人流泪，那场暴雨就是神迹！" 
      voice "voice_grandma_A1.wav"
      lin "石狗在大雨中流血般的眼泪……"
      voice "voice_lin_A1.wav"
  ```

  - 定义角色、播放背景、显示对话并同步语音。

### 模块：UI设计与跳转逻辑

- **目标**：在剧本中加入玩家选择和分支跳转。

示例对话：

- **系统**：设计节点A1之后的选择菜单和跳转逻辑。

- **用户**：假设完成节点A1后，玩家可以选择“继续调查石狗”或“暂时休息”。

- **代理**：我会在 Ren’Py 脚本中添加 `menu` 结构：

  ```
      menu:
          "继续调查石狗":
              jump nodeA2
          "暂时休息":
              jump nodeRest
  ```

  这样，玩家选择后将跳转到相应的剧情节点，实现分支剧情。

以上提示词示例演示了多轮对话结构和各模型的调用逻辑，涵盖从预处理到 Ren’Py 脚本集成的全过程。通过节点编号（如 A1、A2）和图像编号（如 `scene_A1.png`）明确关联跨模态内容，确保生成流程清晰有序。

**参考资料：** 本流程设计参考了学术文献《Generative AI for Cultural Heritage Games: A Case Study on Leizhou Stone Dog》的框架描述和实践结果。
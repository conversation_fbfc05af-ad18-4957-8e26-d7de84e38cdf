"""
RAG检索系统
实现基于SAD和VSI的检索增强生成功能
"""

import os
import json
import configparser
import numpy as np
from typing import List, Dict, Tuple, Any
import pickle
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity

try:
    import renpy
    game_directory = renpy.config.gamedir
except:
    game_directory = os.getcwd()

config = configparser.ConfigParser()
config.read(rf"{game_directory}\config.ini", encoding='utf-8')

class RAGRetrieval:
    def __init__(self):
        self.config = config
        self.similarity_threshold = self.config.getfloat('RAG检索', 'similarity_threshold', fallback=0.7)
        self.vector_db_path = self.config.get('RAG检索', 'vector_db_path', fallback='./vector_db')
        
        # 创建向量数据库目录
        os.makedirs(self.vector_db_path, exist_ok=True)
        
        # 初始化TF-IDF向量化器（简化版本，实际可使用更高级的embedding模型）
        self.vectorizer = TfidfVectorizer(max_features=1000, stop_words=None)
        
        # 存储文档和向量
        self.documents = []
        self.document_vectors = None
        self.document_metadata = []
        
        # 加载现有的向量数据库
        self.load_vector_db()

    def load_vector_db(self):
        """加载现有的向量数据库"""
        try:
            db_file = os.path.join(self.vector_db_path, "vector_db.pkl")
            if os.path.exists(db_file):
                with open(db_file, 'rb') as f:
                    data = pickle.load(f)
                    self.documents = data.get('documents', [])
                    self.document_vectors = data.get('vectors', None)
                    self.document_metadata = data.get('metadata', [])
                    self.vectorizer = data.get('vectorizer', self.vectorizer)
                print(f"加载向量数据库: {len(self.documents)} 个文档")
        except Exception as e:
            print(f"加载向量数据库失败: {e}")

    def save_vector_db(self):
        """保存向量数据库"""
        try:
            db_file = os.path.join(self.vector_db_path, "vector_db.pkl")
            data = {
                'documents': self.documents,
                'vectors': self.document_vectors,
                'metadata': self.document_metadata,
                'vectorizer': self.vectorizer
            }
            with open(db_file, 'wb') as f:
                pickle.dump(data, f)
            print(f"保存向量数据库: {len(self.documents)} 个文档")
        except Exception as e:
            print(f"保存向量数据库失败: {e}")

    def index_sad_files(self, sad_dir: str = None):
        """索引SAD文件到向量数据库"""
        if sad_dir is None:
            sad_dir = os.path.join(game_directory, "SAD")
        
        if not os.path.exists(sad_dir):
            print(f"SAD目录不存在: {sad_dir}")
            return
        
        new_documents = []
        new_metadata = []
        
        for filename in os.listdir(sad_dir):
            if filename.endswith('_SAD.json'):
                file_path = os.path.join(sad_dir, filename)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        sad_data = json.load(f)
                    
                    # 索引每个摘要段落
                    for summary_item in sad_data.get('summaries', []):
                        document = summary_item.get('summary', '')
                        metadata = {
                            'type': 'sad',
                            'source_file': filename,
                            'segment_id': summary_item.get('segment_id'),
                            'material_name': sad_data.get('material_name'),
                            'original_text': summary_item.get('original_text', '')
                        }
                        new_documents.append(document)
                        new_metadata.append(metadata)
                    
                    # 索引整体摘要
                    full_summary = sad_data.get('full_summary', '')
                    if full_summary:
                        metadata = {
                            'type': 'sad_full',
                            'source_file': filename,
                            'material_name': sad_data.get('material_name'),
                            'segment_count': sad_data.get('total_segments', 0)
                        }
                        new_documents.append(full_summary)
                        new_metadata.append(metadata)
                        
                except Exception as e:
                    print(f"索引SAD文件失败 {filename}: {e}")
        
        # 更新文档集合
        self.documents.extend(new_documents)
        self.document_metadata.extend(new_metadata)
        
        # 重新计算向量
        if self.documents:
            self.document_vectors = self.vectorizer.fit_transform(self.documents)
            self.save_vector_db()
            print(f"索引完成: 新增 {len(new_documents)} 个文档")

    def index_vsi_files(self, vsi_dir: str = None):
        """索引VSI文件到向量数据库"""
        if vsi_dir is None:
            vsi_dir = os.path.join(game_directory, "VSI")
        
        if not os.path.exists(vsi_dir):
            print(f"VSI目录不存在: {vsi_dir}")
            return
        
        new_documents = []
        new_metadata = []
        
        for filename in os.listdir(vsi_dir):
            if filename.endswith('_VSI.json'):
                file_path = os.path.join(vsi_dir, filename)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        vsi_data = json.load(f)
                    
                    document = vsi_data.get('description', '')
                    metadata = {
                        'type': 'vsi',
                        'source_file': filename,
                        'image_path': vsi_data.get('image_path'),
                        'image_hash': vsi_data.get('image_hash'),
                        'filename': vsi_data.get('filename')
                    }
                    new_documents.append(document)
                    new_metadata.append(metadata)
                    
                except Exception as e:
                    print(f"索引VSI文件失败 {filename}: {e}")
        
        # 更新文档集合
        self.documents.extend(new_documents)
        self.document_metadata.extend(new_metadata)
        
        # 重新计算向量
        if self.documents:
            self.document_vectors = self.vectorizer.fit_transform(self.documents)
            self.save_vector_db()
            print(f"索引完成: 新增 {len(new_documents)} 个文档")

    def search(self, query: str, top_k: int = 5, doc_type: str = None) -> List[Dict]:
        """检索相关文档"""
        if not self.documents or self.document_vectors is None:
            print("向量数据库为空，请先索引文档")
            return []
        
        # 向量化查询
        query_vector = self.vectorizer.transform([query])
        
        # 计算相似度
        similarities = cosine_similarity(query_vector, self.document_vectors).flatten()
        
        # 获取最相似的文档索引
        top_indices = np.argsort(similarities)[::-1][:top_k]
        
        results = []
        for idx in top_indices:
            similarity = similarities[idx]
            if similarity >= self.similarity_threshold:
                metadata = self.document_metadata[idx]
                
                # 过滤文档类型
                if doc_type and metadata.get('type') != doc_type:
                    continue
                
                result = {
                    'document': self.documents[idx],
                    'similarity': float(similarity),
                    'metadata': metadata
                }
                results.append(result)
        
        return results

    def search_sad(self, query: str, top_k: int = 3) -> List[Dict]:
        """专门检索SAD文档"""
        return self.search(query, top_k, doc_type='sad')

    def search_vsi(self, query: str, top_k: int = 3) -> List[Dict]:
        """专门检索VSI文档"""
        return self.search(query, top_k, doc_type='vsi')

    def get_context_for_node(self, node_description: str, include_images: bool = True) -> Dict:
        """为节点获取相关上下文"""
        context = {
            'text_references': [],
            'image_references': []
        }
        
        # 检索相关文本
        sad_results = self.search_sad(node_description, top_k=3)
        for result in sad_results:
            context['text_references'].append({
                'content': result['document'],
                'source': result['metadata'].get('material_name', ''),
                'similarity': result['similarity'],
                'original_text': result['metadata'].get('original_text', '')
            })
        
        # 检索相关图像（如果需要）
        if include_images:
            vsi_results = self.search_vsi(node_description, top_k=2)
            for result in vsi_results:
                context['image_references'].append({
                    'description': result['document'],
                    'image_path': result['metadata'].get('image_path', ''),
                    'similarity': result['similarity'],
                    'filename': result['metadata'].get('filename', '')
                })
        
        return context

    def rebuild_index(self):
        """重建整个索引"""
        print("重建向量数据库索引...")
        self.documents = []
        self.document_vectors = None
        self.document_metadata = []
        
        # 重新索引所有文件
        self.index_sad_files()
        self.index_vsi_files()
        print("索引重建完成")

    def get_statistics(self) -> Dict:
        """获取数据库统计信息"""
        sad_count = sum(1 for meta in self.document_metadata if meta.get('type') == 'sad')
        vsi_count = sum(1 for meta in self.document_metadata if meta.get('type') == 'vsi')
        
        return {
            'total_documents': len(self.documents),
            'sad_documents': sad_count,
            'vsi_documents': vsi_count,
            'vector_dimensions': self.document_vectors.shape[1] if self.document_vectors is not None else 0
        }

# 使用示例
if __name__ == "__main__":
    rag = RAGRetrieval()
    
    # 重建索引
    rag.rebuild_index()
    
    # 检索示例
    query = "石狗传说"
    results = rag.search(query)
    
    print(f"检索结果 (查询: {query}):")
    for i, result in enumerate(results):
        print(f"{i+1}. 相似度: {result['similarity']:.3f}")
        print(f"   内容: {result['document'][:100]}...")
        print(f"   来源: {result['metadata']}")
        print()
    
    # 获取统计信息
    stats = rag.get_statistics()
    print(f"数据库统计: {stats}")

from openai import OpenAI
import os

SYSTEM_PROMPT = """你是一个视觉小说剧本生成器，请严格遵循以下规则：

【材料使用规范】
1. 核心框架：
- 使用<节点剧情概述>作为故事主脉，必须包含其所有关键节点和转折（即使概述只有一句话）
- 将<参考文献>作为细节库，必须提取其中的人物特征、场景元素、对话风格进行扩展

2. 扩展原则：
* 从参考文献中挖掘以下要素：
  - 人物：提取外貌特征、口头禅、行为习惯
  - 场景：复用文献中的环境描写范式
  - 对话：模仿文献中的对话节奏和俚语使用
* 允许合理扩展：
  - 在节点之间添加过渡场景
  - 增加符合角色设定的日常对话
  - 补充文献中已有的世界观细节

3. 输出要求：
■ 剧本结构：
- 每个场景生成大约2000字，10-20个对话。
- 按照节点剧情概述展开3-7个场景，每个场景约含：
  [背景]（新场景首现时标注）
  "对话"（每次换行间隔）
  [音乐]（高潮/转折点添加）
  
■ 对话规范：
- 每次说话换行，格式：角色名(情绪/动作)："内容"
- 重要对话后添加非语言描述（例：小夏把玩着发梢）
- 每场景至少包含1处文献中的特征引用

4. 禁止事项：
× 添加文献外的剧情支线
× 使用文献未提及的人物特征
× 超过节点概述的时间线范围"""

def read_txt_file(file_path):
    """读取文本文件内容"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read()
    except FileNotFoundError:
        print(f"错误：文件 {file_path} 未找到")
        return ""

def generate_story(api_key, a, b, output_file):
    # 初始化新版客户端
    client = OpenAI(api_key="sk-dbd6a0f99ed648ca8cd0512fe9d12cfa", base_url="https://api.deepseek.com")

    # 用户消息构建保持不变
    user_content = f"节点剧情概述:{a}\n\n参考文献:{b}"  # 保持原有内容

    try:
        # 新版API调用方式
        response = client.chat.completions.create(
            model="deepseek-reasoner",
            messages=[
                {"role": "system", "content": SYSTEM_PROMPT},
                {"role": "user", "content": user_content}
            ],
            temperature=0.7,        stream=False
        )

        # 新版响应结果提取方式
        generated_text = response.choices[0].message.content

        # 保持原有保存逻辑
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(generated_text)

    except Exception as e:
        # 增强错误处理
        print(f"生成失败：{str(e)}")
        if hasattr(e, 'response'):
            print(f"API响应详情：{e.response.text}")

# 使用示例
if __name__ == "__main__":
    # 配置参数
    API_KEY = "sk-dbd6a0f99ed648ca8cd0512fe9d12cfa"  # 替换为实际API密钥
    NODE_FILE = "node.txt"  # 节点剧情文件
    REF_FILE = "reference.txt"     # 参考文献文件
    OUTPUT_FILE = "generated_story.txt"  # 输出文件
    with open(NODE_FILE, 'r', encoding='utf-8') as file:
        a = file.read()
    with open(REF_FILE, 'r', encoding='utf-8') as file:
        b = file.read()
    generate_story(API_KEY, a, b, OUTPUT_FILE)
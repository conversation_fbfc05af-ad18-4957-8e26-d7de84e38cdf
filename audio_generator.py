"""
音频生成模块
实现MusicGen背景音乐生成和GPT-SoVITS语音合成的完整集成
"""

import os
import json
import configparser
from typing import List, Dict, Tuple, Any
from GPT import gpt
from music_generator import generate_music
from local_vocal_generator import generate_audio
from cloud_vocal_generator import online_generate_audio

try:
    import renpy
    game_directory = renpy.config.gamedir
except:
    game_directory = os.getcwd()

config = configparser.ConfigParser()
config.read(rf"{game_directory}\config.ini", encoding='utf-8')

class AudioGenerator:
    def __init__(self):
        self.config = config
        self.audio_dir = os.path.join(game_directory, "audio")
        self.music_dir = os.path.join(self.audio_dir, "music")
        self.voice_dir = os.path.join(self.audio_dir, "voice")
        self.sound_dir = os.path.join(self.audio_dir, "sound")
        
        # 创建音频目录
        for dir_path in [self.audio_dir, self.music_dir, self.voice_dir, self.sound_dir]:
            os.makedirs(dir_path, exist_ok=True)
        
        # 音频类型配置
        self.music_styles = {
            "happy": "upbeat, cheerful, positive melody",
            "sad": "melancholic, slow tempo, minor key",
            "mysterious": "ambient, dark, suspenseful",
            "dramatic": "intense, orchestral, emotional",
            "peaceful": "calm, relaxing, gentle",
            "tense": "fast tempo, dissonant, urgent",
            "romantic": "soft, warm, intimate melody",
            "epic": "grand, orchestral, heroic"
        }
        
        # 角色语音配置
        self.voice_characters = {}
        self.load_voice_character_mapping()

    def load_voice_character_mapping(self):
        """加载角色语音映射"""
        try:
            # 从配置文件读取角色语音映射
            for i in range(1, 7):  # 支持6个角色
                model_key = f"model{i}"
                if self.config.has_option('SOVITS', model_key):
                    model_path = self.config.get('SOVITS', model_key)
                    if model_path:
                        self.voice_characters[f"character_{i}"] = {
                            "model_path": model_path,
                            "voice_index": i
                        }
        except Exception as e:
            print(f"加载语音角色映射失败: {e}")

    def generate_audio_for_node(self, node_narrative: Dict, character_voice_mapping: Dict = None) -> Dict:
        """为节点生成所需的音频"""
        print(f"为节点 {node_narrative.get('node_id')} 生成音频...")
        
        generated_audio = {
            "music_files": [],
            "voice_files": [],
            "sound_files": [],
            "audio_cues": []
        }
        
        narrative_content = node_narrative.get("narrative_content", {})
        node_id = node_narrative.get("node_id", "unknown")
        
        # 1. 生成背景音乐
        music_cues = narrative_content.get("music_cues", [])
        for i, music_cue in enumerate(music_cues):
            music_result = self.generate_background_music(music_cue, node_id, i)
            if music_result:
                generated_audio["music_files"].append(music_result)
        
        # 2. 生成角色语音
        dialogues = narrative_content.get("dialogues", [])
        for i, dialogue in enumerate(dialogues):
            voice_result = self.generate_character_voice(
                dialogue, 
                node_id, 
                i, 
                character_voice_mapping
            )
            if voice_result:
                generated_audio["voice_files"].append(voice_result)
        
        # 3. 生成音效
        sound_cues = narrative_content.get("sound_cues", [])
        for i, sound_cue in enumerate(sound_cues):
            sound_result = self.generate_sound_effect(sound_cue, node_id, i)
            if sound_result:
                generated_audio["sound_files"].append(sound_result)
        
        # 4. 生成音频提示序列
        audio_sequence = self.generate_audio_sequence(narrative_content, generated_audio)
        generated_audio["audio_cues"] = audio_sequence
        
        # 保存生成记录
        self.save_audio_generation_record(node_id, generated_audio)
        
        return generated_audio

    def generate_background_music(self, music_cue: str, node_id: str, index: int = 0) -> Dict:
        """生成背景音乐"""
        print(f"生成背景音乐: {music_cue[:50]}...")
        
        # 1. 分析音乐风格
        music_style = self.analyze_music_style(music_cue)
        
        # 2. 生成音乐描述
        music_description = self.generate_music_description(music_cue, music_style)
        
        # 3. 调用MusicGen生成音乐
        music_filename = f"bgm_{node_id}_{index}"
        success = self.call_music_generation(music_description, music_filename)
        
        if success:
            return {
                "type": "background_music",
                "filename": f"{music_filename}.mp3",
                "file_path": os.path.join(self.music_dir, f"{music_filename}.mp3"),
                "music_cue": music_cue,
                "style": music_style,
                "description": music_description,
                "node_id": node_id,
                "index": index
            }
        
        return None

    def generate_character_voice(self, dialogue: Dict, node_id: str, index: int, character_mapping: Dict = None) -> Dict:
        """生成角色语音"""
        character_name = dialogue.get("character", "")
        text = dialogue.get("text", "")
        emotion_action = dialogue.get("emotion_action", "")
        
        if not text or not character_name:
            return None
        
        print(f"生成角色语音: {character_name} - {text[:30]}...")
        
        # 1. 获取角色语音配置
        voice_config = self.get_character_voice_config(character_name, character_mapping)
        
        # 2. 处理文本（去除特殊字符等）
        processed_text = self.process_voice_text(text)
        
        # 3. 生成语音文件
        voice_filename = f"voice_{node_id}_{character_name}_{index}"
        success = self.call_voice_generation(
            processed_text, 
            voice_config, 
            voice_filename,
            emotion_action
        )
        
        if success:
            return {
                "type": "character_voice",
                "filename": f"{voice_filename}.wav",
                "file_path": os.path.join(self.voice_dir, f"{voice_filename}.wav"),
                "character": character_name,
                "text": text,
                "processed_text": processed_text,
                "emotion_action": emotion_action,
                "voice_config": voice_config,
                "node_id": node_id,
                "index": index
            }
        
        return None

    def generate_sound_effect(self, sound_cue: str, node_id: str, index: int = 0) -> Dict:
        """生成音效（简化实现）"""
        print(f"处理音效提示: {sound_cue[:50]}...")
        
        # 这里可以集成音效生成模型或使用预设音效库
        # 目前提供一个简化的实现
        
        sound_filename = f"sound_{node_id}_{index}"
        
        # 简化处理：记录音效提示，实际音效可以后续手动添加或使用音效库
        return {
            "type": "sound_effect",
            "filename": f"{sound_filename}.wav",
            "file_path": os.path.join(self.sound_dir, f"{sound_filename}.wav"),
            "sound_cue": sound_cue,
            "node_id": node_id,
            "index": index,
            "status": "placeholder"  # 标记为占位符
        }

    def analyze_music_style(self, music_cue: str) -> str:
        """分析音乐风格"""
        music_cue_lower = music_cue.lower()
        
        # 简单的关键词匹配
        for style, keywords in {
            "sad": ["悲伤", "忧郁", "哀愁", "sad", "melancholy"],
            "happy": ["快乐", "欢快", "愉悦", "happy", "cheerful"],
            "mysterious": ["神秘", "诡异", "mysterious", "eerie"],
            "dramatic": ["戏剧", "紧张", "dramatic", "intense"],
            "peaceful": ["平静", "安详", "peaceful", "calm"],
            "tense": ["紧张", "急促", "tense", "urgent"],
            "romantic": ["浪漫", "温柔", "romantic", "gentle"],
            "epic": ["史诗", "宏大", "epic", "grand"]
        }.items():
            if any(keyword in music_cue_lower for keyword in keywords):
                return style
        
        return "peaceful"  # 默认风格

    def generate_music_description(self, music_cue: str, style: str) -> str:
        """生成音乐描述"""
        base_description = self.music_styles.get(style, "calm, ambient music")
        
        # 从提示词库加载
        from prompts.audio_prompts import AUDIO_GENERATION_PROMPTS
        
        system_prompt = AUDIO_GENERATION_PROMPTS["music_description"]["system"]
        user_prompt = AUDIO_GENERATION_PROMPTS["music_description"]["user"].format(
            music_cue=music_cue,
            style=style,
            base_description=base_description
        )

        try:
            enhanced_description = gpt(system_prompt, user_prompt)
            return enhanced_description.strip()
        except Exception as e:
            print(f"音乐描述生成失败: {e}")
            return f"{base_description}, {music_cue}"

    def get_character_voice_config(self, character_name: str, character_mapping: Dict = None) -> Dict:
        """获取角色语音配置"""
        # 优先使用提供的映射
        if character_mapping and character_name in character_mapping:
            return character_mapping[character_name]
        
        # 使用默认映射
        # 简单的角色名到语音索引的映射
        default_mapping = {
            "主角": 1,
            "女主": 2,
            "配角1": 3,
            "配角2": 4,
            "旁白": 6
        }
        
        voice_index = default_mapping.get(character_name, 1)
        
        return {
            "voice_index": voice_index,
            "model_path": self.config.get('SOVITS', f'model{voice_index}', fallback=''),
            "character_name": character_name
        }

    def process_voice_text(self, text: str) -> str:
        """处理语音文本"""
        # 去除特殊字符和标记
        import re
        
        # 去除括号内容（动作描述等）
        text = re.sub(r'\([^)]*\)', '', text)
        
        # 去除特殊符号
        text = re.sub(r'[^\w\s\u4e00-\u9fa5，。！？；：""''（）]', '', text)
        
        # 清理多余空格
        text = ' '.join(text.split())
        
        return text.strip()

    def call_music_generation(self, description: str, filename: str) -> bool:
        """调用音乐生成"""
        try:
            if self.config.getboolean('AI音乐', 'if_on', fallback=False):
                # 调用现有的音乐生成函数
                result = generate_music(filename, description)
                return result == "ok"
            else:
                print("音乐生成功能未启用")
                return False
        except Exception as e:
            print(f"音乐生成失败: {e}")
            return False

    def call_voice_generation(self, text: str, voice_config: Dict, filename: str, emotion: str = "") -> bool:
        """调用语音生成"""
        try:
            voice_index = voice_config.get("voice_index", 1)
            
            if self.config.getboolean('SOVITS', 'if_cloud', fallback=False):
                # 云端语音生成
                result = online_generate_audio(text, voice_index, filename)
                return result == "ok"
            else:
                # 本地语音生成
                sovits_version = "V2" if self.config.get('SOVITS', 'version') == "1" else "V1"
                result = generate_audio(sovits_version, text, voice_index, filename)
                return result == "ok"
        except Exception as e:
            print(f"语音生成失败: {e}")
            return False

    def generate_audio_sequence(self, narrative_content: Dict, generated_audio: Dict) -> List[Dict]:
        """生成音频播放序列"""
        sequence = []
        
        # 按照叙事顺序组织音频
        dialogues = narrative_content.get("dialogues", [])
        music_cues = narrative_content.get("music_cues", [])
        sound_cues = narrative_content.get("sound_cues", [])
        
        # 添加背景音乐
        for music_file in generated_audio.get("music_files", []):
            sequence.append({
                "type": "music",
                "action": "play",
                "file": music_file["filename"],
                "loop": True,
                "fade_in": 1.0
            })
        
        # 添加对话语音
        for voice_file in generated_audio.get("voice_files", []):
            sequence.append({
                "type": "voice",
                "action": "play",
                "file": voice_file["filename"],
                "character": voice_file["character"],
                "text": voice_file["text"]
            })
        
        # 添加音效
        for sound_file in generated_audio.get("sound_files", []):
            if sound_file.get("status") != "placeholder":
                sequence.append({
                    "type": "sound",
                    "action": "play",
                    "file": sound_file["filename"]
                })
        
        return sequence

    def save_audio_generation_record(self, node_id: str, generated_audio: Dict):
        """保存音频生成记录"""
        record_path = os.path.join(self.audio_dir, f"{node_id}_audio_record.json")
        
        # 添加时间戳
        record = {
            **generated_audio,
            "generation_time": self.get_current_time(),
            "node_id": node_id
        }
        
        with open(record_path, 'w', encoding='utf-8') as f:
            json.dump(record, f, ensure_ascii=False, indent=2)

    def get_current_time(self) -> str:
        """获取当前时间字符串"""
        import datetime
        return datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

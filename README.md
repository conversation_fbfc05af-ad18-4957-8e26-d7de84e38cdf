# 2025-01-04 V2.0 Enhanced 重大更新
🎉 **完全重构！基于大纲文档实现的文化遗产严肃游戏生成系统**

## 🚀 核心功能重构
1. **素材预处理模块** - 支持PDF文档、图像语义分析、spaCy文本分段
2. **RAG检索系统** - 智能检索相关文化内容，构建双模态知识库
3. **结构化节点生成** - 明确的剧情节点编号系统(A1, A2, B1等)
4. **增强图像生成** - 结合文化背景的智能Stable Diffusion提示词生成
5. **完整音频系统** - MusicGen背景音乐 + GPT-SoVITS语音合成集成
6. **高级UI设计** - 文化特色界面、无障碍功能、知识收集系统
7. **完整流程控制** - 从素材输入到游戏输出的一键生成
8. **系统测试工具** - 自动化测试和性能优化

## 🎯 技术亮点
- **多阶段处理**: Deepseek-R1 Phase I/II/III分阶段生成
- **标记化输出**: [Setting:]、[Music:]等标记支持后续处理
- **文化遗产专用**: 专门针对文化遗产主题优化的严肃游戏系统
- **模块化设计**: 10个独立模块，易于维护和扩展

## 🎮 新的使用方式
```bash
# 使用增强版启动器（推荐）
python enhanced_launcher.py

# 命令行模式
python main.py enhanced  # 增强生成模式
python main.py test     # 系统测试模式
python main.py help     # 帮助信息
python main.py          # 兼容原有模式
```

# 2024-12-5 V1.4更新日志
1. 重建了GUI界面，更名为AI GAL启动器，更加美观和好用
2. 优化了ai音乐逻辑
3. 重构了部分代码，增加了代码的可读性
4. galgame现在支持转场特效了
5. gpt-sovits支持V2版本
6. 修复了一些bug

# 2024-7-29 V1.3更新日志
1. 新增加了更新器，用户可以直接在软件里更新版本
2. 新增加了发行版功能，用户可以导出现成的游戏剧本，导出后无法再有后续新剧情。
3. 内置rembg安装包，用户部署更方便！
4. 新增ai音乐功能
5. 修复了一些已知bug

# 2024-7-18 V1.2更新日志
1. 新增配置文件的可视化界面GUI,填写配置文件更方便
2. 新增分支选择模式，玩家可以选择ai提供的分支，或者自己填写，同时后续内容的生成速度可能会比先前的版本慢一点。
3. 修复了一些已知bug

# 2024-6-26 V1.1更新日志
1. 新增配置文件，一般设置用户可以直接在config.ini里进行配置，无需打开py文件
2. 新增ai绘画和ai语音的云端模式，再也不吃显卡啦
3. 新增了主题设置，剧本生成前用户可以自己设置剧情的主题
4. 修复了一些已知bug
5. 新增了一张壁纸

## Star History

[![Star History Chart](https://api.star-history.com/svg?repos=tamikip/AI-GAL&type=Date)](https://star-history.com/#tamikip/AI-GAL&Date)


# 🛠️ 环境要求

## 必需配置
- **GPT API密钥** (Deepseek-R1推荐)
- **Python 3.8+** 环境

## 推荐依赖
```bash
pip install spacy requests pillow numpy scikit-learn
python -m spacy download zh_core_web_sm  # 中文文本处理
```

## 可选配置（增强功能）
- **Stable Diffusion API** - 图像生成
- **GPT-SoVITS** - 语音合成
- **MusicGen API** - 背景音乐生成
- **CLIP-Interrogator** - 图像语义分析

## 快速开始
1. 克隆项目：`git clone https://github.com/tamikip/AI-GAL.git`
2. 配置API密钥：编辑 `config.ini`
3. 运行测试：`python main.py test`
4. 启动系统：`python enhanced_launcher.py`

- 📖 项目文档：https://tamikip.github.io/AI-GAL-doc/
- 💬 QQ群：982330586
# 🎮 AI-GAL Enhanced 能做什么？

## 🏛️ 文化遗产严肃游戏生成
专门为文化遗产主题设计的完整游戏生成系统，从原始素材到可运行游戏的端到端解决方案。

## 📚 核心功能模块

### 1. 智能素材处理
- **文档解析**：支持PDF、TXT等多种格式
- **文本分段**：使用spaCy进行智能分段
- **摘要生成**：生成结构化汇总文档(SAD)
- **图像分析**：构建视觉语义库存(VSI)

### 2. RAG增强生成
- **智能检索**：基于向量数据库的语义检索
- **上下文增强**：为生成提供相关文化背景
- **双模态支持**：同时处理文本和图像信息

### 3. 结构化剧情生成
- **框架构建**：生成主线剧情骨架(A1, A2, B1...)
- **角色设定**：详细的角色背景和关系网络
- **节点叙事**：每个节点的详细对话和场景描述

### 4. 多媒体资源生成
- **智能图像**：结合文化背景的Stable Diffusion生成
- **背景音乐**：MusicGen情感化音乐生成
- **角色配音**：GPT-SoVITS高质量语音合成

### 5. 完整游戏整合
- **Ren'Py脚本**：自动生成完整的.rpy文件
- **UI设计**：文化特色界面和交互逻辑
- **分支系统**：智能的选择和跳转机制

## 🚀 快速上手指南

### 第一步：环境配置
1. **安装Python依赖**：
   ```bash
   pip install spacy requests pillow numpy scikit-learn
   python -m spacy download zh_core_web_sm
   ```

2. **配置API密钥**：
   编辑 `config.ini` 文件，填入您的API密钥：
   ```ini
   [CHATGPT]
   gpt_key = your_deepseek_api_key
   base_url = https://api.deepseek.com
   model = deepseek-reasoner
   ```

### 第二步：系统测试
```bash
python main.py test
```
确保所有模块正常工作

### 第三步：开始生成
```bash
python enhanced_launcher.py
```
选择"生成完整游戏"，按提示操作

### 第四步：享受成果
生成完成后，在 `generated_game` 目录找到您的Ren'Py游戏！

## 🆘 问题排查
- **运行测试**：`python main.py test` 检查系统状态
- **查看日志**：检查生成的错误报告文件
- **配置检查**：使用启动器的"配置检查"功能
- **技术支持**：QQ群 982330586
## 许可证

本项目基于 [Ren'Py](https://www.renpy.org/) 项目进行二次开发，并遵循以下许可证条款：

- 本项目的主要部分遵循 [MIT 许可证](LICENSE)。
- 本项目中包含的某些组件和库遵循其他许可证，包括但不限于：
  - [GNU 宽通用公共许可证 (LGPL)](https://www.gnu.org/licenses/lgpl-3.0.html)
  - [Zlib 许可证](https://opensource.org/licenses/Zlib)
  - 其他相关许可证请参见各自的许可证文件。

请确保在分发本项目时，包含所有相关的许可证文件。




# 2025-02-21 V1.5更新日志
1. 新增了多语种支持功能，目前支持英语和日语（包括字幕和语音）
2. 支持快照功能，可以将当前剧情保存，下次可以直接恢复，也可以分享给他人
3. 新增对话模式（β测试），可以与剧情角色一对一聊天
4. 重构优化了游戏代码，优化了gui界面
5. 当前支持deepseek-r1等思考模型
6. 修复云端语音错误的问题
7. rembg软件被废除，改用sd插件模式

# 2024-12-5 V1.4更新日志
1. 重建了GUI界面，更名为AI GAL启动器，更加美观和好用
2. 优化了ai音乐逻辑
3. 重构了部分代码，增加了代码的可读性
4. galgame现在支持转场特效了
5. gpt-sovits支持V2版本
6. 修复了一些bug

# 2024-7-29 V1.3更新日志
1. 新增加了更新器，用户可以直接在软件里更新版本
2. 新增加了发行版功能，用户可以导出现成的游戏剧本，导出后无法再有后续新剧情。
3. 内置rembg安装包，用户部署更方便！
4. 新增ai音乐功能
5. 修复了一些已知bug

# 2024-7-18 V1.2更新日志
1. 新增配置文件的可视化界面GUI,填写配置文件更方便
2. 新增分支选择模式，玩家可以选择ai提供的分支，或者自己填写，同时后续内容的生成速度可能会比先前的版本慢一点。
3. 修复了一些已知bug

# 2024-6-26 V1.1更新日志
1. 新增配置文件，一般设置用户可以直接在config.ini里进行配置，无需打开py文件
2. 新增ai绘画和ai语音的云端模式，再也不吃显卡啦
3. 新增了主题设置，剧本生成前用户可以自己设置剧情的主题
4. 修复了一些已知bug
5. 新增了一张壁纸

## Star History

[![Star History Chart](https://api.star-history.com/svg?repos=tamikip/AI-GAL&type=Date)](https://star-history.com/#tamikip/AI-GAL&Date)


# 在开始使用之前
- ~~首先确保您有一台4GB显存以上，10系以上的显卡。用于运行ai绘画与ai语音~~ 1.1可以自己选择本地还是云端模式
- chatgpt的密钥（或者本地部署LLM）
- 安装好以下程序（云端模式无需理会1和2）：
1. stable diffusion
2. gpt-sovits(可以用其他替代，但是要自己改代码)


- 项目文档：https://tamikip.github.io/AI-GAL-doc/
- qq群:982330586
# AI-GAL有什么用？
https://github.com/user-attachments/assets/2ef16f6e-4650-4bca-995f-d662b39b4542


## 1. 剧情生成
- **使用GPT模型生成：**  
  - Galgame的标题、大纲、背景和人物设定。  
  - 根据设定的主题和要求，生成详细的章节内容，并以对话模式输出。
- **图像生成：**  
  - 利用AI绘画API生成角色和背景图像。  
  - 支持本地和云端绘图模式。  

## 2. 音频生成
- **通过AI合成角色的语音对话。**  
- **支持不同版本的SOVITS模型进行语音合成。**  
- **提供在线和本地音频生成选项。**

## 3. 音乐生成
- **根据情感标签生成背景音乐。**  
- **提供下载生成的音乐文件功能。**


## 4. 分支选择
- **自动生成剧情分支选项，供用户选择不同的剧情走向。**

## 正式开始
### 在第一次打开游戏时，你首先要：
- 安装stable-diffusion，gpt-sovits
- 填写配置参数
### 在运行程序之前，你首先要（云端模式忽略）：
1. 运行stable diffusion的api
2. 运行gpt-sovits的api

### 请从Launcher中进入游戏，资源加载速度取决于您的电脑配置，请耐心等待

## 遇到报错
把log.txt的报错信息复制下来，私信我
## 许可证

本项目基于 [Ren'Py](https://www.renpy.org/) 项目进行二次开发，并遵循以下许可证条款：

- 本项目的主要部分遵循 [MIT 许可证](LICENSE)。
- 本项目中包含的某些组件和库遵循其他许可证，包括但不限于：
  - [GNU 宽通用公共许可证 (LGPL)](https://www.gnu.org/licenses/lgpl-3.0.html)
  - [Zlib 许可证](https://opensource.org/licenses/Zlib)
  - 其他相关许可证请参见各自的许可证文件。

请确保在分发本项目时，包含所有相关的许可证文件。




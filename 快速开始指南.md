# 🚀 AI-GAL Enhanced 快速开始指南

## 📋 准备工作

### 1. 环境要求
- Python 3.8+ 
- 网络连接（用于API调用）

### 2. 安装依赖
```bash
pip install spacy requests pillow numpy scikit-learn
python -m spacy download zh_core_web_sm
```

### 3. 配置API密钥
编辑 `config.ini` 文件：
```ini
[CHATGPT]
gpt_key = your_deepseek_api_key
base_url = https://api.deepseek.com
model = deepseek-reasoner
```

## 🎮 开始使用

### 方式一：交互式启动器（推荐新手）
```bash
python enhanced_launcher.py
```
然后按照菜单提示操作：
1. 选择 "1. 🎮 生成完整游戏"
2. 输入游戏主题（如：雷州石狗文化遗产）
3. 提供素材文件路径，或直接回车使用示例素材
4. 等待生成完成

### 方式二：命令行快速启动
```bash
# 系统测试
python main.py test

# 增强生成模式
python main.py enhanced

# 查看帮助
python main.py help
```

### 方式三：编程调用
```python
from main_pipeline_controller import MainPipelineController

controller = MainPipelineController()
results = controller.generate_complete_game(
    material_paths=["your_material.txt"],
    theme="文化遗产主题",
    reference_materials=["参考关键词"]
)
```

## 📁 素材准备

### 支持的文件格式
- **文本**: .txt, .pdf
- **图像**: .jpg, .jpeg, .png, .bmp, .gif

### 素材建议
- **文本素材**: 文化遗产相关的历史文献、传说故事、学术资料
- **图像素材**: 文物照片、历史图片、场景参考图
- **主题设定**: 具体的文化遗产名称，如"雷州石狗文化"

### 示例素材结构
```
materials/
├── 文化背景.txt          # 历史背景介绍
├── 传说故事.pdf          # 相关传说和故事
├── 文物图片.jpg          # 文物照片
└── 场景参考.png          # 场景参考图
```

## ⚙️ 配置选项

### 基础配置（必需）
```ini
[CHATGPT]
gpt_key = your_api_key           # GPT API密钥
base_url = https://api.deepseek.com
model = deepseek-reasoner
```

### 图像生成配置（可选）
```ini
[AI绘画]
if_cloud = True                  # True=云端, False=本地
draw_key = your_sd_api_key       # Stable Diffusion API密钥
```

### 语音生成配置（可选）
```ini
[SOVITS]
if_cloud = True                  # True=云端, False=本地
语音key = your_voice_api_key     # 语音合成API密钥
```

### 音乐生成配置（可选）
```ini
[AI音乐]
if_on = True                     # 是否启用音乐生成
api_key = your_music_api_key     # 音乐生成API密钥
```

## 🔍 生成流程

### 完整流程（约10-30分钟）
1. **素材预处理** (1-2分钟)
   - 文本分段和摘要
   - 图像语义分析

2. **RAG索引构建** (1分钟)
   - 构建检索数据库

3. **游戏框架生成** (2-3分钟)
   - 生成剧情骨架
   - 创建角色设定

4. **节点叙事生成** (5-10分钟)
   - 为每个节点生成详细剧本

5. **图像生成** (5-15分钟)
   - 生成背景图和角色立绘

6. **音频生成** (3-8分钟)
   - 生成背景音乐和语音

7. **Ren'Py整合** (1-2分钟)
   - 生成完整游戏脚本

8. **UI设计生成** (1分钟)
   - 生成界面和交互逻辑

9. **最终输出** (1分钟)
   - 打包完整游戏

## 📊 输出结果

### 生成的文件
```
generated_game/
├── game/
│   ├── generated_script.rpy    # 主游戏脚本
│   ├── screens.rpy             # UI界面定义
│   ├── options.rpy             # 游戏选项
│   └── gui.rpy                 # GUI配置
├── images/                     # 生成的图像
├── audio/                      # 生成的音频
├── game_info.json             # 游戏信息
└── README.txt                 # 安装说明
```

### 统计信息
生成完成后会显示：
- 故事节点数量
- 角色数量  
- 对话总数
- 生成的图像数量
- 生成的音频数量
- 总耗时

## 🛠️ 故障排除

### 常见问题

**Q: 提示API密钥错误**
A: 检查config.ini中的API密钥是否正确配置

**Q: 生成过程中断**
A: 运行 `python main.py test` 检查系统状态

**Q: 生成的图像质量不佳**
A: 检查Stable Diffusion API配置，或切换到云端模式

**Q: 语音生成失败**
A: 检查GPT-SoVITS配置，或禁用语音生成功能

### 诊断工具

```bash
# 系统测试
python main.py test

# 配置检查（使用启动器）
python enhanced_launcher.py
# 选择 "10. ⚙️ 配置检查"

# 查看详细日志
# 检查生成的错误报告文件
```

### 性能优化

**提高生成速度：**
- 使用云端API而非本地模型
- 减少素材文件大小
- 关闭不必要的功能（如音乐生成）

**提高生成质量：**
- 提供高质量的素材文件
- 使用具体明确的主题描述
- 配置更好的API服务

## 🎯 使用技巧

### 素材准备技巧
1. **文本素材**：提供结构清晰的文档，包含历史背景、人物故事、文化内涵
2. **图像素材**：提供高清的文物照片和场景图片
3. **主题设定**：使用具体的文化遗产名称，如"泉州南音"、"苏州园林"等

### 配置优化技巧
1. **API选择**：Deepseek-R1模型效果最佳
2. **模式选择**：云端模式速度快，本地模式更稳定
3. **功能开关**：根据需要开启/关闭图像、语音、音乐生成

### 结果优化技巧
1. **多次生成**：可以多次运行获得不同效果
2. **手动调整**：生成后可以手动编辑Ren'Py脚本
3. **资源替换**：可以替换生成的图像和音频文件

## 📞 获取帮助

- **在线文档**: https://tamikip.github.io/AI-GAL-doc/
- **QQ群**: 982330586
- **GitHub Issues**: https://github.com/tamikip/AI-GAL/issues

---

🎉 **恭喜！您已经掌握了AI-GAL Enhanced的基本使用方法。现在可以开始创建您的文化遗产严肃游戏了！**

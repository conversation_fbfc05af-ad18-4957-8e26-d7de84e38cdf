"""
增强图像生成模块 (Deepseek-R1 Phase III)
结合VSI和节点描述生成Stable Diffusion提示词，支持正向和负向提示
"""

import os
import json
import configparser
from typing import List, Dict, Tuple, Any
from GPT import gpt
from rag_retrieval import RAGRetrieval
from local_image_generator import generate_image
from cloud_image_generator import online_generate_image

try:
    import renpy
    game_directory = renpy.config.gamedir
except:
    game_directory = os.getcwd()

config = configparser.ConfigParser()
config.read(rf"{game_directory}\config.ini", encoding='utf-8')

class EnhancedImageGenerator:
    def __init__(self):
        self.config = config
        self.rag = RAGRetrieval()
        self.images_dir = os.path.join(game_directory, "images")
        self.prompts_dir = os.path.join(game_directory, "image_prompts")
        os.makedirs(self.images_dir, exist_ok=True)
        os.makedirs(self.prompts_dir, exist_ok=True)
        
        # 图像类型配置
        self.image_types = {
            "background": {
                "width": 1920,
                "height": 1080,
                "style_prefix": "detailed background, cinematic lighting, atmospheric",
                "negative_base": "characters, people, humans, text, watermark"
            },
            "character": {
                "width": 512,
                "height": 768,
                "style_prefix": "character portrait, detailed face, high quality",
                "negative_base": "multiple people, crowd, background focus, blurry"
            },
            "cg": {
                "width": 1920,
                "height": 1080,
                "style_prefix": "detailed CG illustration, dramatic scene",
                "negative_base": "low quality, blurry, text, watermark"
            }
        }

    def generate_images_for_node(self, node_narrative: Dict) -> Dict:
        """为节点生成所需的图像"""
        print(f"为节点 {node_narrative.get('node_id')} 生成图像...")
        
        generated_images = {
            "backgrounds": [],
            "characters": [],
            "cg_images": [],
            "prompts_used": []
        }
        
        narrative_content = node_narrative.get("narrative_content", {})
        
        # 1. 生成背景图像
        settings = narrative_content.get("settings", [])
        for i, setting in enumerate(settings):
            bg_result = self.generate_background_image(
                setting, 
                node_narrative.get("node_id"), 
                i
            )
            if bg_result:
                generated_images["backgrounds"].append(bg_result)
        
        # 2. 生成角色图像（如果需要）
        characters = self.extract_characters_from_narrative(narrative_content)
        for char_name in characters:
            char_result = self.generate_character_image(
                char_name,
                node_narrative.get("context_used", {})
            )
            if char_result:
                generated_images["characters"].append(char_result)
        
        # 3. 生成特殊CG图像
        image_cues = narrative_content.get("image_cues", [])
        for i, cue in enumerate(image_cues):
            cg_result = self.generate_cg_image(
                cue,
                node_narrative.get("node_id"),
                i
            )
            if cg_result:
                generated_images["cg_images"].append(cg_result)
        
        # 保存生成记录
        self.save_generation_record(node_narrative.get("node_id"), generated_images)
        
        return generated_images

    def generate_background_image(self, setting_description: str, node_id: str, index: int = 0) -> Dict:
        """生成背景图像"""
        print(f"生成背景图像: {setting_description[:50]}...")
        
        # 1. 使用Deepseek-R1 Phase III生成提示词
        prompt_data = self.generate_image_prompt(
            setting_description,
            "background",
            node_id
        )
        
        if not prompt_data:
            return None
        
        # 2. 生成图像
        image_name = f"bg_{node_id}_{index}"
        success = self.call_image_generation(
            prompt_data["positive_prompt"],
            prompt_data["negative_prompt"],
            image_name,
            "background"
        )
        
        if success:
            result = {
                "type": "background",
                "image_name": image_name,
                "image_path": os.path.join(self.images_dir, f"{image_name}.png"),
                "setting_description": setting_description,
                "prompts": prompt_data,
                "node_id": node_id
            }
            
            # 更新VSI
            self.update_vsi_with_generated_image(result)
            return result
        
        return None

    def generate_character_image(self, character_name: str, context: Dict) -> Dict:
        """生成角色图像"""
        print(f"生成角色图像: {character_name}")
        
        # 获取角色描述
        character_info = self.get_character_info(character_name, context)
        
        # 生成提示词
        prompt_data = self.generate_image_prompt(
            character_info,
            "character",
            character_name
        )
        
        if not prompt_data:
            return None
        
        # 生成图像
        image_name = f"char_{character_name}"
        success = self.call_image_generation(
            prompt_data["positive_prompt"],
            prompt_data["negative_prompt"],
            image_name,
            "character"
        )
        
        if success:
            result = {
                "type": "character",
                "image_name": image_name,
                "image_path": os.path.join(self.images_dir, f"{image_name}.png"),
                "character_name": character_name,
                "character_info": character_info,
                "prompts": prompt_data
            }
            
            self.update_vsi_with_generated_image(result)
            return result
        
        return None

    def generate_cg_image(self, image_cue: str, node_id: str, index: int = 0) -> Dict:
        """生成CG图像"""
        print(f"生成CG图像: {image_cue[:50]}...")
        
        # 生成提示词
        prompt_data = self.generate_image_prompt(
            image_cue,
            "cg",
            f"{node_id}_cg_{index}"
        )
        
        if not prompt_data:
            return None
        
        # 生成图像
        image_name = f"cg_{node_id}_{index}"
        success = self.call_image_generation(
            prompt_data["positive_prompt"],
            prompt_data["negative_prompt"],
            image_name,
            "cg"
        )
        
        if success:
            result = {
                "type": "cg",
                "image_name": image_name,
                "image_path": os.path.join(self.images_dir, f"{image_name}.png"),
                "cue_description": image_cue,
                "prompts": prompt_data,
                "node_id": node_id
            }
            
            self.update_vsi_with_generated_image(result)
            return result
        
        return None

    def generate_image_prompt(self, description: str, image_type: str, context_id: str) -> Dict:
        """使用Deepseek-R1 Phase III生成图像提示词"""
        
        # 检索相关VSI参考
        vsi_references = self.rag.search_vsi(description, top_k=3)
        vsi_text = self.build_vsi_reference_text(vsi_references)
        
        # 获取图像类型配置
        type_config = self.image_types.get(image_type, self.image_types["background"])
        
        system_prompt = f"""你是一名专业的AI图像生成提示词专家，使用Deepseek-R1 Phase III进行图像提示词生成。

任务要求：
1. 基于场景/角色描述和VSI参考图像，生成高质量的Stable Diffusion提示词
2. 生成正向提示词（positive prompt）和负向提示词（negative prompt）
3. 图像类型：{image_type}
4. 风格要求：{type_config['style_prefix']}
5. 确保提示词具体、详细，能够生成高质量图像

输出格式：
正向提示词：[详细的英文提示词]
负向提示词：[要避免的元素]

注意：
- 使用英文关键词
- 包含画质、风格、细节描述
- 避免版权相关内容
- 确保提示词适合{image_type}类型图像"""

        user_prompt = f"""请为以下描述生成Stable Diffusion提示词：

描述：{description}

VSI参考图像：
{vsi_text}

图像类型：{image_type}
上下文ID：{context_id}

要求：
1. 生成详细的正向提示词
2. 生成合适的负向提示词
3. 确保风格一致性
4. 包含质量和细节关键词"""

        try:
            response = gpt(system_prompt, user_prompt)
            return self.parse_prompt_response(response, type_config)
        except Exception as e:
            print(f"提示词生成失败: {e}")
            return self.generate_default_prompt(description, image_type, type_config)

    def parse_prompt_response(self, response: str, type_config: Dict) -> Dict:
        """解析提示词响应"""
        lines = response.split('\n')
        positive_prompt = ""
        negative_prompt = ""
        
        current_section = None
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            if "正向提示词" in line or "positive" in line.lower():
                current_section = "positive"
                # 提取冒号后的内容
                if ":" in line:
                    positive_prompt = line.split(":", 1)[1].strip()
            elif "负向提示词" in line or "negative" in line.lower():
                current_section = "negative"
                if ":" in line:
                    negative_prompt = line.split(":", 1)[1].strip()
            elif current_section == "positive" and line:
                positive_prompt += " " + line
            elif current_section == "negative" and line:
                negative_prompt += " " + line
        
        # 添加基础风格和质量词
        if positive_prompt:
            positive_prompt = f"{type_config['style_prefix']}, {positive_prompt}, masterpiece, best quality, detailed"
        
        if negative_prompt:
            negative_prompt = f"{type_config['negative_base']}, {negative_prompt}, low quality, blurry, worst quality"
        
        return {
            "positive_prompt": positive_prompt.strip(),
            "negative_prompt": negative_prompt.strip()
        }

    def generate_default_prompt(self, description: str, image_type: str, type_config: Dict) -> Dict:
        """生成默认提示词"""
        # 简单的关键词提取和翻译
        basic_prompt = f"{type_config['style_prefix']}, {description}, masterpiece, best quality"
        basic_negative = f"{type_config['negative_base']}, low quality, blurry"
        
        return {
            "positive_prompt": basic_prompt,
            "negative_prompt": basic_negative
        }

    def build_vsi_reference_text(self, vsi_references: List[Dict]) -> str:
        """构建VSI参考文本"""
        if not vsi_references:
            return "无相关参考图像"
        
        ref_texts = []
        for ref in vsi_references:
            ref_text = f"参考图像: {ref['document']}"
            ref_texts.append(ref_text)
        
        return "\n".join(ref_texts)

    def get_character_info(self, character_name: str, context: Dict) -> str:
        """获取角色信息"""
        characters = context.get("characters", [])
        for char in characters:
            if char.get("name") == character_name:
                info = char.get("description", "")
                if char.get("details"):
                    info += " " + "; ".join(char["details"])
                return info
        
        return f"角色：{character_name}"

    def extract_characters_from_narrative(self, narrative_content: Dict) -> List[str]:
        """从叙事内容中提取角色名"""
        characters = set()
        dialogues = narrative_content.get("dialogues", [])
        
        for dialogue in dialogues:
            char_name = dialogue.get("character", "").strip()
            if char_name and char_name != "旁白":
                characters.add(char_name)
        
        return list(characters)

    def call_image_generation(self, positive_prompt: str, negative_prompt: str, image_name: str, image_type: str) -> bool:
        """调用图像生成"""
        try:
            # 保存提示词
            self.save_prompt_record(image_name, positive_prompt, negative_prompt, image_type)
            
            # 根据配置选择本地或云端生成
            if self.config.getboolean('AI绘画', 'if_cloud', fallback=False):
                # 云端生成（需要适配现有的cloud_image_generator）
                result = online_generate_image(positive_prompt, image_name, image_type)
                return result == "ok"
            else:
                # 本地生成（需要适配现有的local_image_generator）
                result = generate_image(positive_prompt, image_name, image_type)
                return result == "ok"
        except Exception as e:
            print(f"图像生成失败: {e}")
            return False

    def save_prompt_record(self, image_name: str, positive_prompt: str, negative_prompt: str, image_type: str):
        """保存提示词记录"""
        record = {
            "image_name": image_name,
            "image_type": image_type,
            "positive_prompt": positive_prompt,
            "negative_prompt": negative_prompt,
            "timestamp": self.get_current_time()
        }
        
        record_path = os.path.join(self.prompts_dir, f"{image_name}_prompt.json")
        with open(record_path, 'w', encoding='utf-8') as f:
            json.dump(record, f, ensure_ascii=False, indent=2)

    def update_vsi_with_generated_image(self, image_result: Dict):
        """将生成的图像添加到VSI"""
        vsi_entry = {
            "image_path": image_result["image_path"],
            "image_name": image_result["image_name"],
            "description": f"Generated {image_result['type']} image",
            "generation_info": {
                "type": image_result["type"],
                "prompts": image_result.get("prompts", {}),
                "source": "enhanced_image_generator"
            },
            "timestamp": self.get_current_time()
        }
        
        vsi_path = os.path.join(game_directory, "VSI", f"{image_result['image_name']}_generated_VSI.json")
        with open(vsi_path, 'w', encoding='utf-8') as f:
            json.dump(vsi_entry, f, ensure_ascii=False, indent=2)

    def save_generation_record(self, node_id: str, generated_images: Dict):
        """保存生成记录"""
        record_path = os.path.join(self.images_dir, f"{node_id}_generation_record.json")
        with open(record_path, 'w', encoding='utf-8') as f:
            json.dump(generated_images, f, ensure_ascii=False, indent=2)

    def get_current_time(self) -> str:
        """获取当前时间字符串"""
        import datetime
        return datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

# 使用示例
if __name__ == "__main__":
    generator = EnhancedImageGenerator()
    
    # 示例节点叙事
    node_narrative = {
        "node_id": "A1",
        "narrative_content": {
            "settings": ["暮色下的乡村古庙，石狗雕像被雨水冲刷"],
            "dialogues": [
                {"character": "主角", "text": "这里有什么秘密？"}
            ],
            "image_cues": ["石狗雕像特写，眼中似有泪光"]
        },
        "context_used": {
            "characters": [
                {
                    "name": "主角",
                    "description": "年轻的文化研究者",
                    "details": ["好奇心强", "善于观察"]
                }
            ]
        }
    }
    
    # 生成图像
    results = generator.generate_images_for_node(node_narrative)
    
    print("图像生成完成:")
    print(f"背景图像: {len(results['backgrounds'])}")
    print(f"角色图像: {len(results['characters'])}")
    print(f"CG图像: {len(results['cg_images'])}")

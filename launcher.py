"""
AI-GAL 文化遗产游戏生成系统启动器
基于大纲文档实现的完整文化遗产严肃游戏生成系统
"""

import os
import sys
import json
import time
import configparser
from typing import List, Dict

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from main_pipeline_controller import MainPipelineController
from test_and_optimization import GameGenerationTester

class GameLauncher:
    def __init__(self):
        self.version = "2.0.0"
        self.title = "AI-GAL 文化遗产游戏生成系统"
        
        # 检查配置
        self.config = configparser.ConfigParser()
        self.config.read('config.ini', encoding='utf-8')
        
        print(f"""
╔══════════════════════════════════════════════════════════════╗
║                    {self.title}                    ║
║                        版本 {self.version}                         ║
║                                                              ║
║  基于大纲文档实现的完整文化遗产严肃游戏生成系统              ║
║  支持从素材预处理到游戏输出的全流程自动化生成                ║
╚══════════════════════════════════════════════════════════════╝
""")

    def show_main_menu(self):
        """显示主菜单"""
        while True:
            print("\n" + "="*60)
            print("主菜单")
            print("="*60)
            print("1. 🎮 生成完整游戏")
            print("2. 📝 素材预处理")
            print("3. 🔍 RAG检索测试")
            print("4. 🎭 生成游戏框架")
            print("5. 📖 生成节点叙事")
            print("6. 🖼️  生成图像资源")
            print("7. 🎵 生成音频资源")
            print("8. 🎯 整合Ren'Py脚本")
            print("9. 🧪 运行系统测试")
            print("10. ⚙️  配置检查")
            print("11. 📚 查看帮助")
            print("0. 退出")
            print("="*60)
            
            choice = input("请选择功能 (0-11): ").strip()
            
            if choice == "1":
                self.generate_complete_game()
            elif choice == "2":
                self.test_material_preprocessing()
            elif choice == "3":
                self.test_rag_retrieval()
            elif choice == "4":
                self.test_framework_generation()
            elif choice == "5":
                self.test_narrative_generation()
            elif choice == "6":
                self.test_image_generation()
            elif choice == "7":
                self.test_audio_generation()
            elif choice == "8":
                self.test_renpy_integration()
            elif choice == "9":
                self.run_system_test()
            elif choice == "10":
                self.check_configuration()
            elif choice == "11":
                self.show_help()
            elif choice == "0":
                print("感谢使用 AI-GAL 文化遗产游戏生成系统！")
                break
            else:
                print("无效选择，请重新输入")

    def generate_complete_game(self):
        """生成完整游戏"""
        print("\n" + "="*50)
        print("🎮 完整游戏生成")
        print("="*50)
        
        # 获取用户输入
        theme = input("请输入游戏主题 (默认: 雷州石狗文化遗产): ").strip()
        if not theme:
            theme = "雷州石狗文化遗产"
        
        # 素材路径
        print("\n请输入素材文件路径 (支持.txt, .pdf, .jpg, .png等格式)")
        print("多个文件用逗号分隔，直接回车使用示例素材")
        material_input = input("素材路径: ").strip()
        
        if material_input:
            material_paths = [path.strip() for path in material_input.split(',')]
        else:
            # 创建示例素材
            material_paths = self.create_example_materials()
        
        # 参考材料
        ref_input = input("请输入参考材料关键词 (可选，多个用逗号分隔): ").strip()
        reference_materials = [ref.strip() for ref in ref_input.split(',')] if ref_input else None
        
        print(f"\n开始生成游戏...")
        print(f"主题: {theme}")
        print(f"素材: {len(material_paths)} 个文件")
        print(f"参考材料: {reference_materials}")
        
        try:
            # 初始化控制器
            controller = MainPipelineController()
            
            # 执行完整生成流程
            start_time = time.time()
            results = controller.generate_complete_game(
                material_paths=material_paths,
                theme=theme,
                reference_materials=reference_materials
            )
            end_time = time.time()
            
            # 显示结果
            print(f"\n✅ 游戏生成完成！")
            print(f"⏱️  总耗时: {end_time - start_time:.2f} 秒")
            print(f"📁 输出目录: {results['final_output']['game_directory']}")
            
            # 显示统计信息
            stats = results.get('completion_report', {}).get('content_statistics', {})
            print(f"\n📊 生成统计:")
            print(f"   故事节点: {stats.get('story_nodes', 0)}")
            print(f"   角色数量: {stats.get('characters', 0)}")
            print(f"   对话总数: {stats.get('total_dialogues', 0)}")
            print(f"   生成图像: {stats.get('generated_images', 0)}")
            print(f"   生成音频: {stats.get('generated_audio', 0)}")
            
            input("\n按回车键继续...")
            
        except Exception as e:
            print(f"\n❌ 游戏生成失败: {e}")
            print("请检查配置和依赖是否正确安装")
            input("\n按回车键继续...")

    def test_material_preprocessing(self):
        """测试素材预处理"""
        print("\n" + "="*50)
        print("📝 素材预处理测试")
        print("="*50)
        
        try:
            from material_preprocessor import MaterialPreprocessor
            
            preprocessor = MaterialPreprocessor()
            
            # 创建测试文本
            test_text = """
            雷州石狗是广东雷州半岛特有的文化遗产。
            这些石狗雕像承载着深厚的历史文化内涵，
            被当地人视为守护神，具有驱邪避灾的功效。
            每个村庄都有自己的石狗传说和故事。
            """
            
            print("正在处理测试文本...")
            result = preprocessor.process_text_material(test_text, "测试素材")
            
            print(f"✅ 素材预处理完成")
            print(f"📄 SAD文件: {result}")
            
        except Exception as e:
            print(f"❌ 素材预处理失败: {e}")
        
        input("\n按回车键继续...")

    def test_rag_retrieval(self):
        """测试RAG检索"""
        print("\n" + "="*50)
        print("🔍 RAG检索测试")
        print("="*50)
        
        try:
            from rag_retrieval import RAGRetrieval
            
            rag = RAGRetrieval()
            
            # 获取统计信息
            stats = rag.get_statistics()
            print(f"📊 数据库统计:")
            print(f"   总文档数: {stats['total_documents']}")
            print(f"   SAD文档: {stats['sad_documents']}")
            print(f"   VSI文档: {stats['vsi_documents']}")
            
            # 测试检索
            query = input("\n请输入检索查询 (默认: 石狗传说): ").strip()
            if not query:
                query = "石狗传说"
            
            print(f"\n正在检索: {query}")
            results = rag.search(query, top_k=3)
            
            print(f"\n🔍 检索结果 ({len(results)} 条):")
            for i, result in enumerate(results, 1):
                print(f"{i}. 相似度: {result['similarity']:.3f}")
                print(f"   内容: {result['document'][:100]}...")
                print(f"   来源: {result['metadata'].get('material_name', '未知')}")
                print()
            
        except Exception as e:
            print(f"❌ RAG检索测试失败: {e}")
        
        input("\n按回车键继续...")

    def run_system_test(self):
        """运行系统测试"""
        print("\n" + "="*50)
        print("🧪 系统测试")
        print("="*50)
        
        try:
            tester = GameGenerationTester()
            results = tester.run_comprehensive_test()
            
            print(f"\n📊 测试结果:")
            print(f"   总体成功率: {results.get('overall_success_rate', 0):.1%}")
            print(f"   测试耗时: {results.get('total_duration', 0):.2f}秒")
            
            if results.get('recommendations'):
                print(f"\n💡 建议:")
                for i, rec in enumerate(results['recommendations'], 1):
                    print(f"   {i}. {rec}")
            
            if results.get('overall_success_rate', 0) >= 0.8:
                print("\n✅ 系统测试通过，可以正常使用")
            else:
                print("\n⚠️  系统测试发现问题，建议先解决相关问题")
            
        except Exception as e:
            print(f"❌ 系统测试失败: {e}")
        
        input("\n按回车键继续...")

    def check_configuration(self):
        """检查配置"""
        print("\n" + "="*50)
        print("⚙️  配置检查")
        print("="*50)
        
        # 检查关键配置项
        config_items = [
            ("CHATGPT", "gpt_key", "GPT API密钥"),
            ("CHATGPT", "base_url", "GPT API地址"),
            ("CHATGPT", "model", "GPT模型"),
            ("AI绘画", "if_cloud", "图像生成模式"),
            ("SOVITS", "if_cloud", "语音生成模式"),
            ("AI音乐", "if_on", "音乐生成开关")
        ]
        
        print("📋 配置状态:")
        for section, key, description in config_items:
            try:
                value = self.config.get(section, key, fallback="")
                status = "✅ 已配置" if value else "❌ 未配置"
                print(f"   {description}: {status}")
                if value and len(value) > 20:
                    print(f"      值: {value[:20]}...")
                elif value:
                    print(f"      值: {value}")
            except Exception as e:
                print(f"   {description}: ❌ 配置错误 ({e})")
        
        # 检查依赖
        print(f"\n📦 依赖检查:")
        dependencies = [
            ("spacy", "文本处理"),
            ("PIL", "图像处理"),
            ("requests", "网络请求"),
            ("numpy", "数值计算"),
            ("sklearn", "机器学习")
        ]
        
        for package, description in dependencies:
            try:
                __import__(package)
                print(f"   {description} ({package}): ✅ 已安装")
            except ImportError:
                print(f"   {description} ({package}): ❌ 未安装")
        
        input("\n按回车键继续...")

    def show_help(self):
        """显示帮助信息"""
        print("\n" + "="*60)
        print("📚 帮助信息")
        print("="*60)
        
        help_text = """
🎯 系统概述:
   AI-GAL是基于大纲文档实现的完整文化遗产严肃游戏生成系统。
   支持从原始素材到完整Ren'Py游戏的全流程自动化生成。

🔧 主要功能:
   1. 素材预处理 - 文本分段、摘要生成、图像语义分析
   2. RAG检索系统 - 智能检索相关文化内容
   3. 游戏框架生成 - 结构化剧情节点和角色设定
   4. 节点叙事生成 - 详细对话和场景描述
   5. 图像生成 - 基于文化背景的智能图像生成
   6. 音频生成 - 背景音乐和角色语音合成
   7. Ren'Py整合 - 完整游戏脚本生成
   8. UI设计 - 文化特色界面和交互逻辑

📋 使用流程:
   1. 配置API密钥和相关设置
   2. 准备素材文件 (文本、PDF、图像等)
   3. 运行系统测试确保环境正常
   4. 选择"生成完整游戏"开始制作
   5. 等待生成完成，获得可运行的Ren'Py游戏

⚙️  配置要求:
   - GPT API密钥 (必需)
   - Stable Diffusion API (可选，用于图像生成)
   - GPT-SoVITS (可选，用于语音生成)
   - MusicGen API (可选，用于音乐生成)

📞 技术支持:
   - 查看生成的日志文件获取详细错误信息
   - 运行系统测试诊断问题
   - 检查配置确保所有必需项已设置
"""
        
        print(help_text)
        input("\n按回车键继续...")

    def create_example_materials(self) -> List[str]:
        """创建示例素材"""
        example_dir = "example_materials"
        os.makedirs(example_dir, exist_ok=True)
        
        # 创建示例文本
        example_text = """
雷州石狗文化遗产

雷州石狗是广东雷州半岛独特的文化遗产，承载着深厚的历史文化内涵。
这些石狗雕像不仅是艺术品，更是当地人民精神信仰的象征。

历史背景：
石狗文化起源于古代雷州，与当地的民间信仰和传说紧密相关。
据传说，石狗具有驱邪避灾、保佑平安的神奇功效。

文化意义：
每个村庄都有自己的石狗，村民们会定期祭拜，祈求风调雨顺。
石狗见证了雷州半岛的历史变迁，是珍贵的文化遗产。

传说故事：
相传有一只神犬为了保护村民，化身为石狗永远守护着这片土地。
当村民遇到困难时，石狗会显灵相助，因此被奉为守护神。
"""
        
        example_path = os.path.join(example_dir, "雷州石狗文化.txt")
        with open(example_path, 'w', encoding='utf-8') as f:
            f.write(example_text)
        
        print(f"✅ 已创建示例素材: {example_path}")
        return [example_path]

    # 其他测试方法的简化实现
    def test_framework_generation(self):
        print("\n🎭 游戏框架生成测试")
        print("此功能需要完整的素材预处理，建议使用完整游戏生成功能")
        input("按回车键继续...")

    def test_narrative_generation(self):
        print("\n📖 节点叙事生成测试")
        print("此功能需要游戏框架，建议使用完整游戏生成功能")
        input("按回车键继续...")

    def test_image_generation(self):
        print("\n🖼️  图像生成测试")
        print("此功能需要节点叙事，建议使用完整游戏生成功能")
        input("按回车键继续...")

    def test_audio_generation(self):
        print("\n🎵 音频生成测试")
        print("此功能需要节点叙事，建议使用完整游戏生成功能")
        input("按回车键继续...")

    def test_renpy_integration(self):
        print("\n🎯 Ren'Py整合测试")
        print("此功能需要完整的资源，建议使用完整游戏生成功能")
        input("按回车键继续...")

if __name__ == "__main__":
    try:
        launcher = GameLauncher()
        launcher.show_main_menu()
    except KeyboardInterrupt:
        print("\n\n用户中断，程序退出")
    except Exception as e:
        print(f"\n程序运行出错: {e}")
        print("请检查配置和依赖是否正确安装")

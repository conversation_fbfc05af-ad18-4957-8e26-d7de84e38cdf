"""
节点叙事与脚本生成模块 (Deepseek-R1 Phase II)
为每个节点生成详细对话和场景说明，添加[Setting:]、[Music:]等标记支持
"""

import os
import json
import re
import configparser
from typing import List, Dict, Tuple, Any
from GPT import gpt
from rag_retrieval import RAGRetrieval

try:
    import renpy
    game_directory = renpy.config.gamedir
except:
    game_directory = os.getcwd()

config = configparser.ConfigParser()
config.read(rf"{game_directory}\config.ini", encoding='utf-8')

class NodeNarrativeGenerator:
    def __init__(self):
        self.config = config
        self.rag = RAGRetrieval()
        self.narrative_dir = os.path.join(game_directory, "narratives")
        os.makedirs(self.narrative_dir, exist_ok=True)
        
        # 支持的标记类型
        self.supported_tags = {
            "[Setting:]": "场景设置",
            "[Music:]": "背景音乐",
            "[Sound:]": "音效",
            "[Image:]": "图像提示",
            "[Emotion:]": "情感标记",
            "[Action:]": "动作描述"
        }

    def generate_node_narrative(self, node_info: Dict, framework: Dict, previous_nodes: List[Dict] = None) -> Dict:
        """
        为单个节点生成详细叙事内容
        使用Deepseek-R1 Phase II
        """
        print(f"生成节点叙事: {node_info.get('node_id', 'Unknown')}")
        
        # 1. 收集节点上下文
        context = self.collect_node_context(node_info, framework, previous_nodes)
        
        # 2. 生成叙事内容
        narrative_content = self.generate_narrative_content(node_info, context)
        
        # 3. 解析和标记内容
        parsed_content = self.parse_narrative_content(narrative_content)
        
        # 4. 构建完整的节点叙事
        node_narrative = {
            "node_id": node_info.get("node_id"),
            "node_description": node_info.get("description"),
            "narrative_content": parsed_content,
            "context_used": context,
            "generation_metadata": {
                "phase": "Phase II - Node Narrative Generation",
                "model": "Deepseek-R1",
                "timestamp": self.get_current_time()
            }
        }
        
        # 5. 保存节点叙事
        narrative_path = self.save_node_narrative(node_narrative)
        
        print(f"节点叙事生成完成: {narrative_path}")
        return node_narrative

    def collect_node_context(self, node_info: Dict, framework: Dict, previous_nodes: List[Dict] = None) -> Dict:
        """收集节点相关上下文"""
        context = {
            "node_info": node_info,
            "characters": framework.get("characters", []),
            "previous_plot": "",
            "reference_materials": [],
            "related_images": []
        }
        
        # 构建前文情节
        if previous_nodes:
            plot_summary = []
            for prev_node in previous_nodes[-3:]:  # 只取最近3个节点
                if "narrative_content" in prev_node:
                    summary = self.extract_plot_summary(prev_node["narrative_content"])
                    plot_summary.append(f"{prev_node['node_id']}: {summary}")
            context["previous_plot"] = "\n".join(plot_summary)
        
        # 检索相关参考资料
        node_description = node_info.get("description", "")
        if node_description:
            # 检索相关文本
            sad_results = self.rag.search_sad(node_description, top_k=3)
            context["reference_materials"] = [
                {
                    "content": result["document"],
                    "source": result["metadata"].get("material_name", ""),
                    "original_text": result["metadata"].get("original_text", ""),
                    "similarity": result["similarity"]
                }
                for result in sad_results
            ]
            
            # 检索相关图像
            vsi_results = self.rag.search_vsi(node_description, top_k=2)
            context["related_images"] = [
                {
                    "description": result["document"],
                    "image_path": result["metadata"].get("image_path", ""),
                    "similarity": result["similarity"]
                }
                for result in vsi_results
            ]
        
        return context

    def generate_narrative_content(self, node_info: Dict, context: Dict) -> str:
        """生成叙事内容"""
        
        # 构建角色信息
        characters_text = self.build_characters_text(context["characters"])
        
        # 构建参考资料
        references_text = self.build_references_text(context["reference_materials"])
        
        # 构建前文情节
        previous_plot = context.get("previous_plot", "")
        
        # 从提示词库加载
        from prompts.narrative_prompts import NARRATIVE_GENERATION_PROMPTS

        system_prompt = NARRATIVE_GENERATION_PROMPTS["node_narrative"]["system"]
        user_prompt = NARRATIVE_GENERATION_PROMPTS["node_narrative"]["user"].format(
            node_id=node_info.get('node_id'),
            description=node_info.get('description'),
            key_events='; '.join(node_info.get('key_events', [])),
            characters_text=characters_text,
            previous_plot=previous_plot,
            references_text=references_text
        )

        try:
            response = gpt(system_prompt, user_prompt)
            return response
        except Exception as e:
            print(f"叙事内容生成失败: {e}")
            return self.generate_default_narrative(node_info)

    def parse_narrative_content(self, content: str) -> Dict:
        """解析叙事内容，提取标记和对话"""
        parsed = {
            "dialogues": [],
            "narrations": [],
            "settings": [],
            "music_cues": [],
            "sound_cues": [],
            "image_cues": [],
            "emotion_cues": [],
            "action_cues": [],
            "raw_content": content
        }
        
        lines = content.split('\n')
        current_section = "narration"
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # 检测标记
            if line.startswith('[') and ']:' in line:
                tag_match = re.match(r'\[(\w+):\s*([^\]]+)\]', line)
                if tag_match:
                    tag_type = tag_match.group(1).lower()
                    tag_content = tag_match.group(2)
                    
                    if tag_type == "setting":
                        parsed["settings"].append(tag_content)
                    elif tag_type == "music":
                        parsed["music_cues"].append(tag_content)
                    elif tag_type == "sound":
                        parsed["sound_cues"].append(tag_content)
                    elif tag_type == "image":
                        parsed["image_cues"].append(tag_content)
                    elif tag_type == "emotion":
                        parsed["emotion_cues"].append(tag_content)
                    elif tag_type == "action":
                        parsed["action_cues"].append(tag_content)
                continue
            
            # 检测对话
            dialogue_match = re.match(r'([^：:]+)[：:]"([^"]+)"', line)
            if dialogue_match:
                character = dialogue_match.group(1).strip()
                text = dialogue_match.group(2).strip()
                
                # 提取情感/动作标记
                emotion_action = ""
                if '(' in character and ')' in character:
                    emotion_match = re.search(r'\(([^)]+)\)', character)
                    if emotion_match:
                        emotion_action = emotion_match.group(1)
                        character = re.sub(r'\([^)]+\)', '', character).strip()
                
                parsed["dialogues"].append({
                    "character": character,
                    "text": text,
                    "emotion_action": emotion_action
                })
            else:
                # 旁白或描述
                if line and not line.startswith('['):
                    parsed["narrations"].append(line)
        
        return parsed

    def extract_plot_summary(self, narrative_content: Dict) -> str:
        """从叙事内容中提取情节摘要"""
        dialogues = narrative_content.get("dialogues", [])
        narrations = narrative_content.get("narrations", [])
        
        # 简单的摘要生成
        key_dialogues = [d["text"] for d in dialogues[:3]]  # 前3个对话
        key_narrations = narrations[:2] if narrations else []  # 前2个旁白
        
        summary_parts = key_narrations + key_dialogues
        return "; ".join(summary_parts)[:200] + "..."

    def build_characters_text(self, characters: List[Dict]) -> str:
        """构建角色信息文本"""
        if not characters:
            return "暂无角色信息"
        
        char_texts = []
        for char in characters:
            char_text = f"{char.get('name', '未知角色')}: {char.get('description', '')}"
            if char.get('details'):
                char_text += " " + "; ".join(char['details'][:3])
            char_texts.append(char_text)
        
        return "\n".join(char_texts)

    def build_references_text(self, references: List[Dict]) -> str:
        """构建参考资料文本"""
        if not references:
            return "暂无参考资料"
        
        ref_texts = []
        for ref in references:
            ref_text = f"来源：{ref.get('source', '未知')} - {ref.get('content', '')[:300]}"
            if ref.get('original_text'):
                ref_text += f"\n原文：{ref['original_text'][:200]}..."
            ref_texts.append(ref_text)
        
        return "\n\n".join(ref_texts)

    def generate_default_narrative(self, node_info: Dict) -> str:
        """生成默认叙事内容"""
        node_id = node_info.get('node_id', 'A1')
        description = node_info.get('description', '故事开始')
        
        return f"""[Setting: 普通的场景设置]

旁白：{description}

主角："这里发生了什么？"

[Music: 轻松的背景音乐]

旁白：故事在这里展开。

主角："我需要了解更多。"

[Action: 主角环顾四周]

旁白：{node_id}节点的内容在此展开。"""

    def save_node_narrative(self, narrative: Dict) -> str:
        """保存节点叙事"""
        node_id = narrative.get("node_id", "unknown")
        filename = f"narrative_{node_id}.json"
        filepath = os.path.join(self.narrative_dir, filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(narrative, f, ensure_ascii=False, indent=2)
        
        return filepath

    def generate_all_node_narratives(self, framework: Dict) -> List[Dict]:
        """为所有节点生成叙事"""
        print("开始生成所有节点叙事...")
        
        mainline_nodes = framework.get("story_skeleton", {}).get("mainline_nodes", [])
        generated_narratives = []
        
        for i, node in enumerate(mainline_nodes):
            # 获取前面的节点作为上下文
            previous_nodes = generated_narratives[-3:] if generated_narratives else []
            
            # 生成当前节点叙事
            narrative = self.generate_node_narrative(node, framework, previous_nodes)
            generated_narratives.append(narrative)
        
        # 保存所有叙事的汇总
        self.save_all_narratives_summary(generated_narratives)
        
        print(f"所有节点叙事生成完成，共 {len(generated_narratives)} 个节点")
        return generated_narratives

    def save_all_narratives_summary(self, narratives: List[Dict]):
        """保存所有叙事的汇总"""
        summary = {
            "total_nodes": len(narratives),
            "generation_time": self.get_current_time(),
            "narratives_overview": [
                {
                    "node_id": n.get("node_id"),
                    "description": n.get("node_description"),
                    "dialogue_count": len(n.get("narrative_content", {}).get("dialogues", [])),
                    "has_settings": len(n.get("narrative_content", {}).get("settings", [])) > 0,
                    "has_music": len(n.get("narrative_content", {}).get("music_cues", [])) > 0
                }
                for n in narratives
            ]
        }
        
        summary_path = os.path.join(self.narrative_dir, "narratives_summary.json")
        with open(summary_path, 'w', encoding='utf-8') as f:
            json.dump(summary, f, ensure_ascii=False, indent=2)

    def get_current_time(self) -> str:
        """获取当前时间字符串"""
        import datetime
        return datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

# 使用示例
if __name__ == "__main__":
    generator = NodeNarrativeGenerator()
    
    # 示例节点信息
    node_info = {
        "node_id": "A1",
        "description": "主角在雷州博物馆发现石狗的秘密",
        "key_events": ["参观博物馆", "发现线索", "遇到神秘人物"],
        "characters": ["主角", "博物馆管理员"]
    }
    
    # 示例框架
    framework = {
        "characters": [
            {
                "name": "主角",
                "description": "对文化遗产感兴趣的年轻研究者",
                "details": ["好奇心强", "善于观察", "有文化素养"]
            }
        ]
    }
    
    # 生成节点叙事
    narrative = generator.generate_node_narrative(node_info, framework)
    
    print("节点叙事生成完成:")
    print(f"对话数量: {len(narrative['narrative_content']['dialogues'])}")
    print(f"场景设置: {narrative['narrative_content']['settings']}")
    print(f"音乐提示: {narrative['narrative_content']['music_cues']}")

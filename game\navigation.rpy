# 导航逻辑文件

# 节点解锁系统
init python:
    def unlock_node(node_id):
        """解锁节点"""
        if node_id in node_map:
            node_map[node_id]["unlocked"] = True
            renpy.save_persistent()
    
    def is_node_unlocked(node_id):
        """检查节点是否解锁"""
        return node_map.get(node_id, {}).get("unlocked", False)
    
    def visit_node(node_id):
        """标记节点为已访问"""
        if node_id in node_map:
            node_map[node_id]["visited"] = True
            renpy.save_persistent()

# 分支选择逻辑
label choice_handler(choices):
    call screen choice_menu(choices)
    return

# 文化知识检查
label cultural_knowledge_check(required_level):
    if cultural_knowledge_level >= required_level:
        return True
    else:
        "您需要更多的文化知识才能解锁此选项。"
        return False


# 节点地图数据
default node_map = {
    "A1": {
        "label": "node_A1",
        "title": "主角开始探索雷州石狗文化遗产的旅程",
        "unlocked": true,
        "visited": false
    }
}

# 分支逻辑数据
default branch_logic = {}

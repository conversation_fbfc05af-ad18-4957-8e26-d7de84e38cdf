# Generated by AI-GAL Enhanced System
# 文化遗产严肃游戏脚本

# 定义角色颜色
define gui.accent_color = '#cc6600'
define gui.idle_color = '#888888'
define gui.hover_color = '#ffffff'

# 角色定义
define 主角 = Character("主角", color="#ff6b6b")
define narrator = Character(None)

# 图像定义

# 主脚本

label start:
    # 游戏开始
    jump node_A1

label node_A1:
    # 节点 A1

    "旁白：主角开始探索雷州石狗文化遗产的旅程"
    "旁白：故事在这里展开。"
    "旁白：A1节点的内容在此展开。"
    主角 "这里发生了什么？"
    主角 "我需要了解更多。"
    jump node_A2

# 菜单和分支

label end_game:
    "游戏结束，感谢您的体验！"
    return


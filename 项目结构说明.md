# AI-GAL Enhanced 项目结构说明

## 📁 核心文件结构

```
AI-GAL/
├── 🚀 启动文件
│   ├── enhanced_launcher.py          # 增强版交互式启动器（推荐）
│   └── main.py                       # 主程序（兼容原版+新增功能）
│
├── 🧠 核心模块
│   ├── material_preprocessor.py      # 素材预处理模块
│   ├── rag_retrieval.py             # RAG检索系统
│   ├── game_framework_generator.py   # 游戏框架生成
│   ├── node_narrative_generator.py   # 节点叙事生成
│   ├── enhanced_image_generator.py   # 增强图像生成
│   ├── enhanced_audio_generator.py   # 增强音频生成
│   ├── renpy_integrator.py          # Ren'Py整合模块
│   ├── ui_design_module.py          # UI设计模块
│   ├── main_pipeline_controller.py  # 主流程控制器
│   └── test_and_optimization.py     # 测试优化模块
│
├── 🔧 基础组件
│   ├── GPT.py                       # GPT API调用
│   ├── local_image_generator.py     # 本地图像生成
│   ├── cloud_image_generator.py     # 云端图像生成
│   ├── local_vocal_generator.py     # 本地语音生成
│   ├── cloud_vocal_generator.py     # 云端语音生成
│   └── music_generator.py           # 音乐生成
│
├── ⚙️ 配置文件
│   └── config.ini                   # 系统配置文件
│
├── 📚 文档
│   ├── README.md                    # 项目说明
│   ├── 大纲文档.md                   # 原始需求文档
│   ├── 项目完成总结.md               # 完成总结
│   └── 项目结构说明.md               # 本文档
│
└── 📂 生成目录
    ├── SAD/                         # 汇总文档存储
    ├── VSI/                         # 视觉语义库存
    ├── framework/                   # 游戏框架存储
    ├── narratives/                  # 节点叙事存储
    ├── image_prompts/               # 图像提示词存储
    ├── audio/                       # 音频文件存储
    ├── vector_db/                   # 向量数据库
    ├── game/                        # Ren'Py脚本存储
    └── generated_game/              # 最终游戏输出
```

## 🎯 模块功能说明

### 启动文件
- **enhanced_launcher.py**: 新的交互式启动器，提供菜单式操作界面
- **main.py**: 保持向后兼容，支持命令行参数调用新功能

### 核心模块（按处理流程排序）
1. **material_preprocessor.py**: 处理输入素材，生成SAD和VSI
2. **rag_retrieval.py**: 构建和查询向量数据库
3. **game_framework_generator.py**: 生成游戏框架和角色设定
4. **node_narrative_generator.py**: 为每个节点生成详细叙事
5. **enhanced_image_generator.py**: 生成游戏所需图像
6. **enhanced_audio_generator.py**: 生成音频和音乐
7. **renpy_integrator.py**: 整合为Ren'Py脚本
8. **ui_design_module.py**: 生成UI界面和交互逻辑
9. **main_pipeline_controller.py**: 统一流程控制
10. **test_and_optimization.py**: 系统测试和优化

### 基础组件
- **GPT.py**: 统一的GPT API调用接口
- **图像生成模块**: 支持本地和云端两种模式
- **语音生成模块**: 支持本地和云端两种模式
- **music_generator.py**: 背景音乐生成

## 🔄 数据流程

```
原始素材 → 素材预处理 → RAG索引 → 游戏框架 → 节点叙事 → 多媒体生成 → Ren'Py整合 → 完整游戏
    ↓           ↓          ↓         ↓         ↓          ↓           ↓          ↓
   文本/图像    SAD/VSI    向量库    剧情骨架   详细剧本    图像/音频    .rpy脚本   可运行游戏
```

## 🗂️ 生成目录说明

### 中间文件存储
- **SAD/**: 存储文本摘要和汇总文档
- **VSI/**: 存储图像语义分析结果
- **framework/**: 存储游戏框架和角色设定
- **narratives/**: 存储节点叙事内容
- **image_prompts/**: 存储图像生成提示词
- **audio/**: 存储生成的音频文件
- **vector_db/**: 存储RAG检索的向量数据库

### 最终输出
- **game/**: Ren'Py脚本文件(.rpy)
- **generated_game/**: 完整的可运行游戏包

## 🚀 使用方式

### 1. 交互式启动（推荐）
```bash
python enhanced_launcher.py
```
提供友好的菜单界面，适合新用户

### 2. 命令行启动
```bash
python main.py enhanced    # 增强生成模式
python main.py test       # 系统测试
python main.py help       # 帮助信息
python main.py            # 兼容原版模式
```

### 3. 模块化调用
每个模块都可以独立导入和使用：
```python
from main_pipeline_controller import MainPipelineController
controller = MainPipelineController()
results = controller.generate_complete_game(...)
```

## 🔧 配置说明

### 必需配置
```ini
[CHATGPT]
gpt_key = your_api_key
base_url = https://api.deepseek.com
model = deepseek-reasoner
```

### 可选配置
```ini
[AI绘画]
if_cloud = True/False

[SOVITS]
if_cloud = True/False

[AI音乐]
if_on = True/False

[素材预处理]
spacy_model = zh_core_web_sm
enable_pdf_processing = True

[RAG检索]
similarity_threshold = 0.7
```

## 📊 系统特点

### 技术优势
- **模块化设计**: 每个模块独立，易于维护和扩展
- **多阶段处理**: Phase I/II/III确保生成质量
- **RAG增强**: 检索增强生成保证内容相关性
- **标记化输出**: 统一的标记系统便于后续处理

### 用户体验
- **一键生成**: 从素材到游戏的完整自动化
- **进度反馈**: 详细的生成进度和状态显示
- **错误处理**: 完善的错误报告和恢复机制
- **测试工具**: 自动化的系统测试和诊断

### 文化价值
- **专业定制**: 专门为文化遗产主题优化
- **教育功能**: 支持严肃游戏的教育目标
- **内容准确**: 基于真实文化素材的生成

## 🔮 扩展性

### 模块扩展
- 每个模块都有清晰的接口，便于功能扩展
- 支持新的AI模型和API集成
- 可以添加新的文化主题模板

### 技术升级
- 支持更先进的多模态模型
- 可以集成更多的生成技术
- 便于云端部署和分布式处理

这个清理后的项目结构更加清晰，专注于核心功能，去除了原项目的冗余部分，为用户提供了更好的使用体验。

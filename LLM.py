import os
from openai import OpenAI

def get_summary(text):
    client = OpenAI(api_key="sk-dbd6a0f99ed648ca8cd0512fe9d12cfa", base_url="https://api.deepseek.com")
    response = client.chat.completions.create(
        model="deepseek-reasoner",
        messages=[
            {"role": "system", "content": "你是一个专业的故事摘要生成器，需要为长文本创建结构化摘要。任务要求：\n"
                                          "1. 严格提取原文中的角色设定、关键场景、核心冲突、悬念伏笔\n"
                                          "2. 每个要素用3-5个逗号分隔的关键词+短语描述\n"
                                          "3. 补充50字内的叙事脉络说明故事发展逻辑\n"
                                          "4. 保留所有专有名称、时空关系等检索关键要素\n"
                                          "5. 禁止添加任何推测性内容\n"
                                          "6. 总字数尽量控制在200字内，主要保证故事内容完整的提取。"},
            {"role": "user", "content": f"请为以下故事生成结构化摘要：\n\n待处理文本：\n{text}\n\n输出格式要求：\n"
                                        "角色设定：[人物A（特征）, 人物B（身份）]\n"
                                        "关键场景：[地点X（现象）, 时间Y（事件）]\n"
                                        "核心冲突：[目标矛盾], [身份危机]\n"
                                        "悬念伏笔：[神秘物品], [异常现象]\n"
                                        "叙事脉络：[按时间线简述故事发展逻辑]"}
        ],
        stream=False
    )
    return response.choices[0].message.content

def process_txt_files(input_folder, output_file):
    with open(output_file, 'a', encoding='utf-8') as out_file:
        for filename in os.listdir(input_folder):
            if filename.endswith(".txt"):
                file_path = os.path.join(input_folder, filename)
                with open(file_path, 'r', encoding='utf-8') as file:
                    text = file.read()
                    summary = get_summary(text)
                    print(summary)
                    out_file.write(f"\n=== {filename} ===\n")
                    out_file.write(summary + "\n")

if __name__ == "__main__":
    input_folder = "C:\\Users\\<USER>\\Desktop\\papers\\text"  # 你的txt文件存放目录
    output_file = "summary.txt"  # 结果存放文件
    process_txt_files(input_folder, output_file)
    print("所有摘要已生成并追加到 summary.txt！")

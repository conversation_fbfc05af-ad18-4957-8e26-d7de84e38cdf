"""
游戏框架生成模块 (Deepseek-R1 Phase I)
基于SAD生成结构化主线剧情节点和角色设定
"""

import os
import json
import configparser
from typing import List, Dict, Tuple
from GPT import gpt
from rag_retrieval import RAGRetrieval

try:
    import renpy
    game_directory = renpy.config.gamedir
except:
    game_directory = os.getcwd()

config = configparser.ConfigParser()
config.read(rf"{game_directory}\config.ini", encoding='utf-8')

class GameFrameworkGenerator:
    def __init__(self):
        self.config = config
        self.rag = RAGRetrieval()
        self.framework_dir = os.path.join(game_directory, "framework")
        os.makedirs(self.framework_dir, exist_ok=True)

    def generate_story_framework(self, theme: str = None, reference_materials: List[str] = None) -> Dict:
        """
        生成游戏主线剧情框架和角色设定
        使用Deepseek-R1 Phase I
        """
        print("开始生成游戏框架 (Phase I)...")
        
        # 1. 收集参考资料
        context = self.collect_reference_context(theme, reference_materials)
        
        # 2. 生成主线剧情骨架
        story_skeleton = self.generate_story_skeleton(theme, context)
        
        # 3. 生成角色设定
        characters = self.generate_character_settings(story_skeleton, context)
        
        # 4. 构建完整框架
        framework = {
            "theme": theme or "文化遗产探索",
            "story_skeleton": story_skeleton,
            "characters": characters,
            "reference_context": context,
            "generation_metadata": {
                "phase": "Phase I - Framework Generation",
                "model": "Deepseek-R1",
                "timestamp": self.get_current_time()
            }
        }
        
        # 5. 保存框架
        framework_path = self.save_framework(framework)
        
        print(f"游戏框架生成完成: {framework_path}")
        return framework

    def collect_reference_context(self, theme: str, reference_materials: List[str] = None) -> Dict:
        """收集参考上下文"""
        context = {
            "sad_summaries": [],
            "vsi_descriptions": [],
            "theme_related": []
        }
        
        # 如果有主题，检索相关内容
        if theme:
            sad_results = self.rag.search_sad(theme, top_k=5)
            context["sad_summaries"] = [
                {
                    "content": result["document"],
                    "source": result["metadata"].get("material_name", ""),
                    "similarity": result["similarity"]
                }
                for result in sad_results
            ]
            
            vsi_results = self.rag.search_vsi(theme, top_k=3)
            context["vsi_descriptions"] = [
                {
                    "description": result["document"],
                    "image_path": result["metadata"].get("image_path", ""),
                    "similarity": result["similarity"]
                }
                for result in vsi_results
            ]
        
        # 处理指定的参考材料
        if reference_materials:
            for material in reference_materials:
                material_results = self.rag.search(material, top_k=3)
                context["theme_related"].extend([
                    {
                        "content": result["document"],
                        "source": result["metadata"].get("material_name", ""),
                        "type": result["metadata"].get("type", ""),
                        "similarity": result["similarity"]
                    }
                    for result in material_results
                ])
        
        return context

    def generate_story_skeleton(self, theme: str, context: Dict) -> Dict:
        """生成主线剧情骨架"""
        
        # 构建参考资料文本
        reference_text = self.build_reference_text(context)
        
        system_prompt = """你是一名专业的视觉小说游戏剧情架构师，使用Deepseek-R1 Phase I进行高层故事结构设计。

任务要求：
1. 基于提供的汇总文档(SAD)和参考资料，构建主线故事骨架
2. 生成结构化的剧情节点，使用明确的编号系统（如A1, A2, B1等）
3. 每个节点包含：节点编号、简要描述、关键事件、涉及角色
4. 确保剧情具有清晰的起承转合结构
5. 控制信息密度，每个节点内容充实但不过载
6. 引用检索到的资料，增加剧情与文化内涵的一致性

输出格式：
- 主线节点列表（至少8个节点）
- 每个节点的详细信息
- 节点间的逻辑关系
- 支线节点（可选）

注意：不要使用markdown格式，输出纯文本结构化内容。"""

        user_prompt = f"""请基于以下信息生成游戏主线剧情骨架：

主题：{theme}

参考资料：
{reference_text}

要求：
1. 生成至少8个主线节点（A1-A8）
2. 可以添加2-3个支线节点（B1-B3）
3. 每个节点包含编号、描述、关键事件、涉及角色
4. 确保剧情连贯性和文化内涵
5. 引用参考资料中的具体内容"""

        try:
            response = gpt(system_prompt, user_prompt)
            return self.parse_story_skeleton(response)
        except Exception as e:
            print(f"剧情骨架生成失败: {e}")
            return self.generate_default_skeleton(theme)

    def parse_story_skeleton(self, response: str) -> Dict:
        """解析剧情骨架响应"""
        skeleton = {
            "mainline_nodes": [],
            "sideline_nodes": [],
            "node_relationships": []
        }
        
        lines = response.split('\n')
        current_node = None
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # 检测节点编号
            if line.startswith(('A', 'B')) and ':' in line:
                if current_node:
                    # 保存前一个节点
                    if current_node["node_id"].startswith('A'):
                        skeleton["mainline_nodes"].append(current_node)
                    else:
                        skeleton["sideline_nodes"].append(current_node)
                
                # 开始新节点
                parts = line.split(':', 1)
                node_id = parts[0].strip()
                description = parts[1].strip() if len(parts) > 1 else ""
                
                current_node = {
                    "node_id": node_id,
                    "description": description,
                    "key_events": [],
                    "characters": [],
                    "references": []
                }
            elif current_node and line:
                # 添加到当前节点的详细信息
                if "关键事件" in line or "事件" in line:
                    current_node["key_events"].append(line)
                elif "角色" in line or "人物" in line:
                    current_node["characters"].append(line)
                elif "参考" in line or "引用" in line:
                    current_node["references"].append(line)
                else:
                    # 添加到描述中
                    current_node["description"] += " " + line
        
        # 保存最后一个节点
        if current_node:
            if current_node["node_id"].startswith('A'):
                skeleton["mainline_nodes"].append(current_node)
            else:
                skeleton["sideline_nodes"].append(current_node)
        
        return skeleton

    def generate_character_settings(self, story_skeleton: Dict, context: Dict) -> List[Dict]:
        """生成角色设定"""
        
        # 从剧情骨架中提取角色信息
        mentioned_characters = set()
        for node in story_skeleton.get("mainline_nodes", []):
            for char_info in node.get("characters", []):
                # 简单的角色名提取
                if ":" in char_info:
                    char_name = char_info.split(":")[0].strip()
                    mentioned_characters.add(char_name)
        
        reference_text = self.build_reference_text(context)
        
        system_prompt = """你是一名角色设计专家，需要为视觉小说游戏创建详细的角色设定。

要求：
1. 基于剧情骨架和参考资料设计角色
2. 确定一位平凡的主角，便于玩家代入
3. 每个角色包含：姓名、年龄、性别、外貌、性格、背景、动机
4. 角色设定要与文化背景和剧情主题一致
5. 角色间要有合理的关系网络

输出格式：
角色名：详细设定
（包含外貌、性格、背景、在剧情中的作用等）"""

        user_prompt = f"""请基于以下信息设计游戏角色：

剧情骨架中提及的角色：{', '.join(mentioned_characters)}

参考资料：
{reference_text}

要求：
1. 设计3-5个主要角色
2. 确定一位主角（平凡人设定）
3. 每个角色要有详细的设定
4. 角色要符合文化背景"""

        try:
            response = gpt(system_prompt, user_prompt)
            return self.parse_character_settings(response)
        except Exception as e:
            print(f"角色设定生成失败: {e}")
            return self.generate_default_characters()

    def parse_character_settings(self, response: str) -> List[Dict]:
        """解析角色设定响应"""
        characters = []
        lines = response.split('\n')
        current_character = None
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # 检测角色名
            if ':' in line and not line.startswith(' '):
                if current_character:
                    characters.append(current_character)
                
                char_name = line.split(':')[0].strip()
                char_description = line.split(':', 1)[1].strip() if ':' in line else ""
                
                current_character = {
                    "name": char_name,
                    "description": char_description,
                    "details": []
                }
            elif current_character and line:
                current_character["details"].append(line)
        
        if current_character:
            characters.append(current_character)
        
        return characters

    def build_reference_text(self, context: Dict) -> str:
        """构建参考资料文本"""
        text_parts = []
        
        # SAD摘要
        if context.get("sad_summaries"):
            text_parts.append("文本摘要：")
            for summary in context["sad_summaries"][:3]:
                text_parts.append(f"- {summary['content'][:200]}...")
        
        # VSI描述
        if context.get("vsi_descriptions"):
            text_parts.append("\n图像描述：")
            for desc in context["vsi_descriptions"][:2]:
                text_parts.append(f"- {desc['description']}")
        
        # 主题相关
        if context.get("theme_related"):
            text_parts.append("\n相关资料：")
            for item in context["theme_related"][:3]:
                text_parts.append(f"- {item['content'][:150]}...")
        
        return '\n'.join(text_parts)

    def generate_default_skeleton(self, theme: str) -> Dict:
        """生成默认剧情骨架"""
        return {
            "mainline_nodes": [
                {
                    "node_id": "A1",
                    "description": f"主角开始探索{theme}的旅程",
                    "key_events": ["初次接触主题", "建立基本设定"],
                    "characters": ["主角"],
                    "references": []
                }
            ],
            "sideline_nodes": [],
            "node_relationships": []
        }

    def generate_default_characters(self) -> List[Dict]:
        """生成默认角色设定"""
        return [
            {
                "name": "主角",
                "description": "一位对文化遗产感兴趣的年轻人",
                "details": ["平凡的背景", "好奇心强", "容易代入"]
            }
        ]

    def save_framework(self, framework: Dict) -> str:
        """保存游戏框架"""
        timestamp = self.get_current_time().replace(':', '-').replace(' ', '_')
        filename = f"game_framework_{timestamp}.json"
        filepath = os.path.join(self.framework_dir, filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(framework, f, ensure_ascii=False, indent=2)
        
        # 同时保存为最新版本
        latest_path = os.path.join(self.framework_dir, "latest_framework.json")
        with open(latest_path, 'w', encoding='utf-8') as f:
            json.dump(framework, f, ensure_ascii=False, indent=2)
        
        return filepath

    def load_latest_framework(self) -> Dict:
        """加载最新的游戏框架"""
        latest_path = os.path.join(self.framework_dir, "latest_framework.json")
        if os.path.exists(latest_path):
            with open(latest_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        return {}

    def get_current_time(self) -> str:
        """获取当前时间字符串"""
        import datetime
        return datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

# 使用示例
if __name__ == "__main__":
    generator = GameFrameworkGenerator()
    
    # 生成游戏框架
    framework = generator.generate_story_framework(
        theme="雷州石狗文化遗产",
        reference_materials=["石狗传说", "文化历史"]
    )
    
    print("游戏框架生成完成:")
    print(f"主线节点数: {len(framework['story_skeleton']['mainline_nodes'])}")
    print(f"角色数量: {len(framework['characters'])}")
    
    # 显示主线节点
    for node in framework['story_skeleton']['mainline_nodes']:
        print(f"{node['node_id']}: {node['description']}")

import os
from openai import OpenAI

def count_summaries(summary_text):
    # 假设每个故事概述之间有一个空行分隔
    summaries = [s.strip() for s in summary_text.split('\n\n') if s.strip()]
    return len(summaries)

def generate_story(summary_text, summary_count):
    min_mainline_nodes = max(1, int(summary_count * 0.7))  # 至少 70% 数量的主线节点

    client = OpenAI(api_key="sk-dbd6a0f99ed648ca8cd0512fe9d12cfa", base_url="https://api.deepseek.com")
    response = client.chat.completions.create(
        model="deepseek-reasoner",
        messages=[
            {"role": "system", "content": f"你是一名写年轻人喜欢玩的视觉小说游戏剧情写作者，任务是根据提供的多个故事概述生成完整的主线剧情骨架，并可扩展支线情节。同时，你需要生成游戏中出场的人物介绍，并确定一位平凡的主角。你可以进行自由发挥，不必严格参考原故事，但应保持合理性。任务要求：\n"
                                          "1. 深度挖掘所有概述中的共同点，构建一个连贯的主线剧情骨架，主线必需是比较庞大的，有深度，有一定戏剧性冲突的，不一定很详细。\n"
                                          "2. 主线剧情骨架必须具有清晰的结构，并使用明确的剧情节点标识（如 A1, A2, B1 这样的层级结构）。\n"
                                          "3. 主线剧情节点数量不得少于 {min_mainline_nodes} 个。\n"
                                          "4. 每个剧情节点需注明引用的概述文件，最多引用两个文件，最少一个，引用出处必须准确（例如：‘石狗见嫜流眼泪’.txt），并且引用要和剧情不在同一行。\n"
                                          "5. 详细描述主线的起因、发展、高潮和结局，确保逻辑清晰、紧密联系。\n"
                                          "6. 设计合理的支线故事，增强世界观深度，使故事更丰满，支线也要编号，并且给出从哪个节点的支线，是否能回到主线，会去到主线的哪个节点（不一定回到原节点），请用在编号处体现支线完成去向，如果会导致游戏结束则直接用(A1,NULL)表示。每个支线剧情也需注明引用的概述文件。\n"
                                          "7. 时间线上尽量参考原始故事顺序，但可以自由调整以保证逻辑合理。\n"
                                          "8. 生成时请基于‘什么人，在哪里，做什么事’的结构进行组织，但不要直接在故事文本中出现此结构。\n"
                                          "9. 确定一位主角，该主角应当是一个平凡人，以便玩家代入。描述其背景、性格、动机，并确保其在故事中的核心作用。\n"
                                          "10. 输出格式：主线故事（包含清晰标识的剧情节点及引用信息）+ 支线故事（包含引用信息）+ 角色介绍（描述所有游戏中出场角色的背景、性格、动机等）+ 确定的主角信息。\n"
                                          "11. 禁止输出 Markdown 语法，如`# 标题`、`**加粗**`等。\n"
                                          "12. 禁止输出代码格式，如```代码块```，仅输出纯文本。\n"
                                          "13. 禁止使用列表符号（如`-`、`*`、`1.`等），所有内容应以流畅的文本方式呈现。"},
            {"role": "user", "content": f"请根据以下多个故事概述，整理并扩展成完整的主线故事，并合理设计支线情节。同时，为游戏生成角色介绍，并明确一位平凡的主角。请使用 A1, A2, B1 这样的结构标识剧情节点，并在每个节点标注引用的概述文件（例如：‘石狗见嫜流眼泪’.txt），确保主线剧情节点不少于 {min_mainline_nodes} 个。\n\n{summary_text}"}
        ],
        stream=False
    )
    return response.choices[0].message.content

def process_summary_file(input_file, output_file):
    with open(input_file, 'r', encoding='utf-8') as file:
        summary_text = file.read()

    summary_count = count_summaries(summary_text)
    print(f"检测到 {summary_count} 个故事概述。")

    full_story = generate_story(summary_text, summary_count)

    with open(output_file, 'w', encoding='utf-8') as out_file:
        out_file.write(full_story + "\n")

    print(f"完整故事已生成并保存到 {output_file}！")

if __name__ == "__main__":
    input_file = "summaries.txt"  # 包含多个故事概述的文件
    output_file = "generated_story.txt"  # 生成的完整故事存放文件
    process_summary_file(input_file, output_file)

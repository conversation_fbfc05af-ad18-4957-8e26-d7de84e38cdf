"""
素材预处理模块
实现文本分段、摘要生成(SAD)和图像语义分析(VSI)功能
"""

import os
import json
import configparser
import spacy
import PyPDF2
from PIL import Image
import requests
from typing import List, Dict, Tuple
from GPT import gpt
import hashlib

try:
    import renpy
    game_directory = renpy.config.gamedir
except:
    game_directory = os.getcwd()

config = configparser.ConfigParser()
config.read(rf"{game_directory}\config.ini", encoding='utf-8')

class MaterialPreprocessor:
    def __init__(self):
        self.config = config
        self.spacy_model = self.config.get('素材预处理', 'spacy_model', fallback='zh_core_web_sm')
        self.max_chunk_size = self.config.getint('素材预处理', 'max_text_chunk_size', fallback=1000)
        self.enable_pdf = self.config.getboolean('素材预处理', 'enable_pdf_processing', fallback=True)
        
        # 初始化spaCy模型
        try:
            self.nlp = spacy.load(self.spacy_model)
        except OSError:
            print(f"警告：spaCy模型 {self.spacy_model} 未找到，请安装：python -m spacy download {self.spacy_model}")
            self.nlp = None
        
        # 创建输出目录
        self.sad_dir = os.path.join(game_directory, "SAD")
        self.vsi_dir = os.path.join(game_directory, "VSI")
        os.makedirs(self.sad_dir, exist_ok=True)
        os.makedirs(self.vsi_dir, exist_ok=True)

    def extract_text_from_pdf(self, pdf_path: str) -> str:
        """从PDF文件提取文本"""
        if not self.enable_pdf:
            return ""
        
        try:
            with open(pdf_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                text = ""
                for page in pdf_reader.pages:
                    text += page.extract_text() + "\n"
                return text
        except Exception as e:
            print(f"PDF提取失败 {pdf_path}: {e}")
            return ""

    def segment_text(self, text: str) -> List[str]:
        """使用spaCy进行文本分段"""
        if not self.nlp:
            # 如果spaCy不可用，使用简单的句号分割
            segments = [s.strip() for s in text.split('。') if s.strip()]
            return segments
        
        doc = self.nlp(text)
        segments = []
        current_segment = ""
        
        for sent in doc.sents:
            if len(current_segment + sent.text) > self.max_chunk_size:
                if current_segment:
                    segments.append(current_segment.strip())
                current_segment = sent.text
            else:
                current_segment += sent.text
        
        if current_segment:
            segments.append(current_segment.strip())
        
        return segments

    def generate_summary(self, text_segment: str) -> str:
        """使用Deepseek-R1生成文本段落摘要"""
        system_prompt = """你是一个文本摘要专家，需要为输入的文本段落生成抽象性摘要。
要求：
1. 保留关键信息和核心内容
2. 压缩冗余信息，提高信息密度
3. 保持原文的主要观点和逻辑结构
4. 摘要长度控制在原文的30-50%
5. 使用简洁明了的语言"""
        
        user_prompt = f"请为以下文本生成抽象性摘要：\n\n{text_segment}"
        
        try:
            summary = gpt(system_prompt, user_prompt)
            return summary.strip()
        except Exception as e:
            print(f"摘要生成失败: {e}")
            return text_segment[:200] + "..."  # 降级处理

    def process_text_material(self, text_content: str, material_name: str) -> str:
        """处理文本素材，生成SAD"""
        print(f"处理文本素材: {material_name}")
        
        # 1. 文本分段
        segments = self.segment_text(text_content)
        print(f"分段完成，共 {len(segments)} 个段落")
        
        # 2. 逐段生成摘要
        summaries = []
        for i, segment in enumerate(segments):
            print(f"生成摘要 {i+1}/{len(segments)}")
            summary = self.generate_summary(segment)
            summaries.append({
                "segment_id": i + 1,
                "original_text": segment,
                "summary": summary,
                "source": material_name
            })
        
        # 3. 生成汇总文档(SAD)
        sad_content = {
            "material_name": material_name,
            "total_segments": len(segments),
            "summaries": summaries,
            "full_summary": self.generate_full_summary(summaries)
        }
        
        # 4. 保存SAD文件
        sad_filename = f"{material_name}_SAD.json"
        sad_path = os.path.join(self.sad_dir, sad_filename)
        with open(sad_path, 'w', encoding='utf-8') as f:
            json.dump(sad_content, f, ensure_ascii=False, indent=2)
        
        print(f"SAD文件已保存: {sad_path}")
        return sad_path

    def generate_full_summary(self, summaries: List[Dict]) -> str:
        """生成整体汇总摘要"""
        all_summaries = "\n".join([s["summary"] for s in summaries])
        
        system_prompt = """你是一个文档整理专家，需要将多个段落摘要整合成一个连贯的整体摘要。
要求：
1. 保持逻辑连贯性
2. 突出主要主题和关键信息
3. 去除重复内容
4. 形成完整的叙述结构"""
        
        user_prompt = f"请将以下段落摘要整合成一个连贯的整体摘要：\n\n{all_summaries}"
        
        try:
            full_summary = gpt(system_prompt, user_prompt)
            return full_summary.strip()
        except Exception as e:
            print(f"整体摘要生成失败: {e}")
            return all_summaries

    def analyze_image_with_clip(self, image_path: str) -> str:
        """使用CLIP-Interrogator分析图像"""
        # 这里需要集成CLIP-Interrogator
        # 由于CLIP-Interrogator需要特定的环境配置，这里提供一个简化版本
        try:
            # 简化版本：使用GPT-4V或其他视觉模型分析图像
            # 实际实现中应该使用CLIP-Interrogator
            return self.analyze_image_simple(image_path)
        except Exception as e:
            print(f"图像分析失败 {image_path}: {e}")
            return f"图像文件: {os.path.basename(image_path)}"

    def analyze_image_simple(self, image_path: str) -> str:
        """简化的图像分析方法"""
        # 获取图像基本信息
        try:
            with Image.open(image_path) as img:
                width, height = img.size
                mode = img.mode
                
            filename = os.path.basename(image_path)
            description = f"图像文件: {filename}, 尺寸: {width}x{height}, 模式: {mode}"
            
            # 这里可以添加更复杂的图像分析逻辑
            # 例如调用在线图像识别API或本地模型
            
            return description
        except Exception as e:
            print(f"图像分析失败: {e}")
            return f"图像文件: {os.path.basename(image_path)}"

    def process_image_material(self, image_path: str) -> str:
        """处理图像素材，生成VSI"""
        print(f"处理图像素材: {image_path}")
        
        # 1. 图像分析
        description = self.analyze_image_with_clip(image_path)
        
        # 2. 生成VSI条目
        image_hash = self.get_file_hash(image_path)
        vsi_entry = {
            "image_path": image_path,
            "image_hash": image_hash,
            "description": description,
            "filename": os.path.basename(image_path),
            "processed_time": self.get_current_time()
        }
        
        # 3. 保存到VSI
        vsi_filename = f"{os.path.splitext(os.path.basename(image_path))[0]}_VSI.json"
        vsi_path = os.path.join(self.vsi_dir, vsi_filename)
        with open(vsi_path, 'w', encoding='utf-8') as f:
            json.dump(vsi_entry, f, ensure_ascii=False, indent=2)
        
        print(f"VSI文件已保存: {vsi_path}")
        return vsi_path

    def get_file_hash(self, file_path: str) -> str:
        """计算文件哈希值"""
        hash_md5 = hashlib.md5()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()

    def get_current_time(self) -> str:
        """获取当前时间字符串"""
        import datetime
        return datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    def process_materials(self, material_paths: List[str]) -> Dict[str, List[str]]:
        """批量处理素材"""
        results = {
            "sad_files": [],
            "vsi_files": []
        }
        
        for path in material_paths:
            if not os.path.exists(path):
                print(f"文件不存在: {path}")
                continue
            
            file_ext = os.path.splitext(path)[1].lower()
            
            if file_ext == '.pdf':
                # 处理PDF文件
                text_content = self.extract_text_from_pdf(path)
                if text_content:
                    sad_path = self.process_text_material(text_content, os.path.basename(path))
                    results["sad_files"].append(sad_path)
            elif file_ext == '.txt':
                # 处理文本文件
                with open(path, 'r', encoding='utf-8') as f:
                    text_content = f.read()
                sad_path = self.process_text_material(text_content, os.path.basename(path))
                results["sad_files"].append(sad_path)
            elif file_ext in ['.jpg', '.jpeg', '.png', '.bmp', '.gif']:
                # 处理图像文件
                vsi_path = self.process_image_material(path)
                results["vsi_files"].append(vsi_path)
            else:
                print(f"不支持的文件类型: {path}")
        
        return results

# 使用示例
if __name__ == "__main__":
    preprocessor = MaterialPreprocessor()
    
    # 示例：处理素材
    material_paths = [
        "example.txt",
        "example.pdf", 
        "example.jpg"
    ]
    
    results = preprocessor.process_materials(material_paths)
    print("处理完成:")
    print(f"SAD文件: {results['sad_files']}")
    print(f"VSI文件: {results['vsi_files']}")
